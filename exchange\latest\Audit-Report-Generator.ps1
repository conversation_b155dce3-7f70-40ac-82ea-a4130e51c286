#Requires -Version 5.1

# ================================================================================
# **Created By:** E.Z. Consultancy
# **Script Version:** 1.1.0 (Enhanced Version)
# **Exchange Version:** Exchange Server 2016/2019/Online Compatible
# 🏛️ **Authority:** Internal Audit
# ================================================================================

<#
================================================================================
Exchange Mailbox Security Audit Report Generator (Enhanced Version)
================================================================================
Description: Enhanced version with improved visual formatting, detailed permission
analysis, and comprehensive reporting features while maintaining parsing stability.
================================================================================
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [string]$JsonFilePath = "",

    [Parameter(Mandatory = $false)]
    [string]$OutputPath = "",

    [Parameter(Mandatory = $false)]
    [switch]$ConsoleOutput
)

# Initialize report generation session
$ReportStartTime = Get-Date
$ReportID = [System.Guid]::NewGuid()

# Get the current script filename dynamically
$ScriptFileName = try {
    if ($MyInvocation.MyCommand.Name) {
        $MyInvocation.MyCommand.Name
    } elseif ($MyInvocation.MyCommand.Path) {
        Split-Path -Leaf $MyInvocation.MyCommand.Path
    } elseif ($PSCommandPath) {
        Split-Path -Leaf $PSCommandPath
    } else {
        "Exchange-Audit-Report-Generator.ps1"  # Fallback name
    }
} catch {
    "Exchange-Audit-Report-Generator.ps1"  # Fallback name in case of error
}

Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Exchange Mailbox Security Audit Report Generator (Test Version)" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Report ID: $ReportID" -ForegroundColor Yellow
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host ""

# ================================================================================
# HELPER FUNCTIONS
# ================================================================================

function Find-LatestJsonFile {
    param([string]$SearchPath = ".")
    
    try {
        $JsonFiles = Get-ChildItem -Path $SearchPath -Filter "*.json" -ErrorAction SilentlyContinue |
            Where-Object { $_.Name -like "*Exchange*" -or $_.Name -like "*audit*" -or $_.Name -like "*Sample*" } |
            Sort-Object LastWriteTime -Descending
        
        if ($JsonFiles -and $JsonFiles.Count -gt 0) {
            return $JsonFiles[0].FullName
        }
        return $null
    }
    catch {
        Write-Host "[ERROR] Failed to search for JSON files: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

function ConvertTo-SafeHtml {
    param([string]$Text)

    if ([string]::IsNullOrWhiteSpace($Text)) {
        return "N/A"
    }

    # Simple HTML encoding
    $Text = $Text -replace "&", "&amp;"
    $Text = $Text -replace "<", "&lt;"
    $Text = $Text -replace ">", "&gt;"
    $Text = $Text -replace '"', "&quot;"
    $Text = $Text -replace "'", "&#39;"

    return $Text
}

function Format-UserDisplayName {
    param(
        [string]$UserDisplayName,
        [string]$UserEmail,
        [string]$UserAccount
    )

    # Extract username from domain\username format if needed
    $Username = if ($UserAccount) { $UserAccount -replace ".*\\", "" } else { "" }

    # Check if this is a service account
    $ServiceAccountPrefixes = @("svc-", "service-", "admin-", "sa-", "system-", "app-", "srv-")
    $IsServiceAccount = $false

    foreach ($Prefix in $ServiceAccountPrefixes) {
        if ($Username.ToLower().StartsWith($Prefix.ToLower()) -or
            $UserDisplayName.ToLower().Contains($Prefix.ToLower()) -or
            $UserEmail.ToLower().Contains($Prefix.ToLower())) {
            $IsServiceAccount = $true
            break
        }
    }

    # Format the display name based on account type
    if ($IsServiceAccount) {
        # Service Account: Display name + (Service Account)
        return "$UserDisplayName (Service Account)"
    } else {
        # Human User: Display name + (email)
        if ($UserEmail -and $UserEmail -ne "N/A") {
            return "$UserDisplayName ($UserEmail)"
        } else {
            # If no email provided, try to generate one from display name or username
            $GeneratedEmail = ""
            if ($Username -and $Username -ne "") {
                # Use the username from domain\username format
                $GeneratedEmail = "$<EMAIL>"
            } elseif ($UserDisplayName -and $UserDisplayName -ne "") {
                # Generate email from display name (e.g., "Mary Johnson" -> "<EMAIL>")
                $EmailPrefix = $UserDisplayName.ToLower() -replace "\s+", "." -replace "[^a-z0-9\.]", ""
                $GeneratedEmail = "$<EMAIL>"
            }

            if ($GeneratedEmail -ne "") {
                return "$UserDisplayName ($GeneratedEmail)"
            } else {
                return $UserDisplayName
            }
        }
    }
}

function Format-MailboxDisplayName {
    param(
        [string]$MailboxDisplayName,
        [string]$MailboxEmail
    )

    # Always format as: Display Name (<EMAIL>)
    if ($MailboxEmail -and $MailboxEmail -ne "N/A") {
        return "$MailboxDisplayName ($MailboxEmail)"
    } else {
        return $MailboxDisplayName
    }
}

# ================================================================================
# INPUT VALIDATION AND FILE PROCESSING
# ================================================================================

Write-Host "Step 1/3: Validating input parameters and locating JSON file..." -ForegroundColor Yellow

# Determine JSON file path
if ([string]::IsNullOrWhiteSpace($JsonFilePath)) {
    Write-Host "  No JSON file specified, searching for latest audit results..." -ForegroundColor Gray
    $JsonFilePath = Find-LatestJsonFile
    
    if ([string]::IsNullOrWhiteSpace($JsonFilePath)) {
        Write-Host "[ERROR] No JSON audit files found in current directory." -ForegroundColor Red
        Write-Host "Please specify a JSON file path using -JsonFilePath parameter." -ForegroundColor Red
        exit 1
    }
    
    Write-Host "  Found JSON file: $JsonFilePath" -ForegroundColor Green
}

# Validate JSON file exists
if (-not (Test-Path -Path $JsonFilePath)) {
    Write-Host "[ERROR] JSON file not found: $JsonFilePath" -ForegroundColor Red
    exit 1
}

Write-Host "  JSON file validated: $JsonFilePath" -ForegroundColor Green

# Determine output path
if ([string]::IsNullOrWhiteSpace($OutputPath)) {
    $JsonFileName = [System.IO.Path]::GetFileNameWithoutExtension($JsonFilePath)
    $OutputPath = ".\$JsonFileName-Report-$(Get-Date -Format 'yyyyMMdd-HHmmss').html"
}

Write-Host "  Output path: $OutputPath" -ForegroundColor Green

# ================================================================================
# JSON DATA LOADING AND VALIDATION
# ================================================================================

Write-Host "Step 2/3: Loading and validating JSON audit data..." -ForegroundColor Yellow

try {
    $JsonContent = Get-Content -Path $JsonFilePath -Raw -Encoding UTF8
    $AuditData = $JsonContent | ConvertFrom-Json -ErrorAction Stop
    
    Write-Host "  JSON data loaded successfully" -ForegroundColor Green
    
    # Extract key sections
    $AuditMetadata = if ($AuditData.AuditMetadata) { $AuditData.AuditMetadata } else { @{} }
    $ComplianceSummary = if ($AuditData.ComplianceSummary) { $AuditData.ComplianceSummary } else { @{} }
    
    Write-Host "  Audit ID: $($AuditMetadata.AuditID)" -ForegroundColor Gray
    Write-Host "  Organization: $($AuditMetadata.OrganizationName)" -ForegroundColor Gray
    
    # Validate key sections exist
    $ControlSections = @(
        "MBX_1_1_ImpersonationRights",
        "MBX_2_1_FullAccessPermissions", 
        "MBX_3_1_AuditLogging",
        "MBX_4_1_SendAsPermissions",
        "MBX_5_1_SendOnBehalfPermissions"
    )
    
    $FoundSections = @()
    $ErrorSections = @()
    
    foreach ($Section in $ControlSections) {
        if ($AuditData.$Section) {
            if ($AuditData.$Section.Error) {
                $ErrorSections += $Section
            } else {
                $FoundSections += $Section
            }
        }
    }
    
    Write-Host "  Found control sections: $($FoundSections.Count)" -ForegroundColor Green
    Write-Host "  Error sections: $($ErrorSections.Count)" -ForegroundColor $(if ($ErrorSections.Count -gt 0) { "Yellow" } else { "Green" })
    
    if ($AuditData.AdministratorDiscovery) {
        Write-Host "  Administrator discovery data: Available" -ForegroundColor Green
    } else {
        Write-Host "  Administrator discovery data: Not available" -ForegroundColor Yellow
    }
    
}
catch {
    Write-Host "[ERROR] Failed to load or parse JSON file: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# ================================================================================
# SIMPLE REPORT GENERATION
# ================================================================================

Write-Host "Step 3/3: Generating simple HTML report..." -ForegroundColor Yellow

# Process control data
$ControlSummaryData = @()

# Define control mappings
$ControlMappings = @{
    "MBX_1_1_ImpersonationRights" = @{ ID = "MBX-1.1"; Name = "Mailbox Impersonation Rights"; Category = "Access Control" }
    "MBX_2_1_FullAccessPermissions" = @{ ID = "MBX-2.1"; Name = "Full Access Permissions"; Category = "Delegation Management" }
    "MBX_3_1_AuditLogging" = @{ ID = "MBX-3.1"; Name = "Audit Logging Configuration"; Category = "Compliance & Monitoring" }
    "MBX_4_1_SendAsPermissions" = @{ ID = "MBX-4.1"; Name = "Send-As Permissions"; Category = "Email Delegation" }
    "MBX_5_1_SendOnBehalfPermissions" = @{ ID = "MBX-5.1"; Name = "Send-On-Behalf Permissions"; Category = "Email Delegation" }
}

foreach ($ControlKey in $ControlMappings.Keys) {
    $ControlData = $AuditData.$ControlKey
    $Mapping = $ControlMappings[$ControlKey]
    
    if ($ControlData) {
        $ControlSummaryData += [PSCustomObject]@{
            ControlID = if ($ControlData.ControlID) { $ControlData.ControlID } else { $Mapping.ID }
            ControlName = if ($ControlData.ControlName) { $ControlData.ControlName } else { $Mapping.Name }
            Category = $Mapping.Category
            RiskLevel = if ($ControlData.RiskLevel) { $ControlData.RiskLevel } else { "Unknown" }
            ComplianceStatus = if ($ControlData.ComplianceStatus) { $ControlData.ComplianceStatus } else { "Unknown" }
            ComplianceScore = if ($ControlData.ComplianceScore) { $ControlData.ComplianceScore } else { 0 }
            CVSSScore = if ($ControlData.CVSSScore) { $ControlData.CVSSScore } else { 0 }
            Finding = if ($ControlData.Finding) { $ControlData.Finding } else { "No findings available" }
            Recommendation = if ($ControlData.Recommendation) { $ControlData.Recommendation } else { "No recommendations available" }
            HasError = if ($ControlData.Error) { $true } else { $false }
            ErrorMessage = if ($ControlData.Error) { $ControlData.Error } else { "" }
        }
    }
}

# Calculate dashboard metrics
$TotalControls = $ControlSummaryData.Count
$CompliantControls = ($ControlSummaryData | Where-Object { $_.ComplianceStatus -eq "Compliant" }).Count
$ReviewRequiredControls = ($ControlSummaryData | Where-Object { $_.ComplianceStatus -eq "Review Required" }).Count
$NonCompliantControls = ($ControlSummaryData | Where-Object { $_.ComplianceStatus -eq "Non-Compliant" }).Count
$ErrorControls = ($ControlSummaryData | Where-Object { $_.HasError }).Count

$OverallComplianceScore = if ($TotalControls -gt 0) { 
    [math]::Round(($ControlSummaryData | Measure-Object -Property ComplianceScore -Average).Average, 1) 
} else { 0 }

# Risk level distribution
$CriticalRiskControls = ($ControlSummaryData | Where-Object { $_.RiskLevel -eq "Critical" }).Count
$HighRiskControls = ($ControlSummaryData | Where-Object { $_.RiskLevel -eq "High" }).Count
$MediumRiskControls = ($ControlSummaryData | Where-Object { $_.RiskLevel -eq "Medium" }).Count
$LowRiskControls = ($ControlSummaryData | Where-Object { $_.RiskLevel -eq "Low" }).Count

Write-Host "  Dashboard metrics calculated:" -ForegroundColor Green
Write-Host "    - Total Controls: $TotalControls" -ForegroundColor Gray
Write-Host "    - Compliant: $CompliantControls" -ForegroundColor Gray
Write-Host "    - Overall Compliance Score: $OverallComplianceScore%" -ForegroundColor Gray

# Generate simple HTML using StringBuilder approach
$Html = New-Object System.Text.StringBuilder

$null = $Html.AppendLine('<!DOCTYPE html>')
$null = $Html.AppendLine('<html lang="en">')
$null = $Html.AppendLine('<head>')
$null = $Html.AppendLine('<meta charset="UTF-8">')
$null = $Html.AppendLine('<meta name="viewport" content="width=device-width, initial-scale=1.0">')
$null = $Html.AppendLine("<title>Exchange Mailbox Security Audit Report - $($AuditMetadata.OrganizationName)</title>")
$null = $Html.AppendLine('<style>')
$null = $Html.AppendLine('body { font-family: Arial, sans-serif; margin: 20px; background-color: #f5f5f5; }')
$null = $Html.AppendLine('.container { max-width: 1400px; margin: 0 auto; background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }')
$null = $Html.AppendLine('.header { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; padding: 30px; border-radius: 8px; margin-bottom: 30px; }')
$null = $Html.AppendLine('.header h1 { margin: 0 0 10px 0; font-size: 2.5em; }')
$null = $Html.AppendLine('.dashboard { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; margin-bottom: 30px; }')
$null = $Html.AppendLine('.card { background: white; padding: 20px; border-radius: 8px; box-shadow: 0 2px 8px rgba(0,0,0,0.1); border-left: 4px solid #667eea; }')
$null = $Html.AppendLine('.card h3 { margin: 0 0 15px 0; color: #667eea; }')
$null = $Html.AppendLine('.card .value { font-size: 2.5em; font-weight: bold; margin-bottom: 10px; }')
$null = $Html.AppendLine('table { width: 100%; border-collapse: collapse; margin: 20px 0; }')
$null = $Html.AppendLine('th, td { padding: 12px; text-align: left; border-bottom: 1px solid #ddd; }')
$null = $Html.AppendLine('th { background-color: #f8f9fa; font-weight: bold; }')
$null = $Html.AppendLine('tr:hover { background-color: #f5f5f5; }')
$null = $Html.AppendLine('.badge { padding: 4px 12px; border-radius: 20px; font-size: 0.8em; font-weight: bold; color: white; }')
$null = $Html.AppendLine('.risk-critical { background-color: #dc3545; }')
$null = $Html.AppendLine('.risk-high { background-color: #fd7e14; }')
$null = $Html.AppendLine('.risk-medium { background-color: #ffc107; color: #212529; }')
$null = $Html.AppendLine('.risk-low { background-color: #28a745; }')
$null = $Html.AppendLine('.status-compliant { background-color: #28a745; }')
$null = $Html.AppendLine('.status-review { background-color: #fd7e14; }')
$null = $Html.AppendLine('.status-non-compliant { background-color: #dc3545; }')
$null = $Html.AppendLine('.footer { text-align: center; margin-top: 40px; padding: 20px; border-top: 1px solid #ddd; color: #666; font-size: 0.8em; }')
$null = $Html.AppendLine('')
$null = $Html.AppendLine('/* Permission Analysis Sections */')
$null = $Html.AppendLine('.permission-section { margin: 30px 0; padding: 20px; background: #f8f9fa; border-radius: 8px; border-left: 4px solid #007bff; }')
$null = $Html.AppendLine('.permission-list { margin-top: 15px; }')
$null = $Html.AppendLine('.permission-item { background: white; margin: 10px 0; padding: 15px; border-radius: 6px; border-left: 3px solid #e9ecef; }')
$null = $Html.AppendLine('.permission-description { font-weight: bold; color: #333; margin-bottom: 8px; }')
$null = $Html.AppendLine('.dept-info { font-size: 0.9em; margin: 5px 0; padding: 5px 10px; border-radius: 4px; }')
$null = $Html.AppendLine('.dept-info.same-dept { background: #d4edda; color: #155724; }')
$null = $Html.AppendLine('.dept-info.cross-dept { background: #f8d7da; color: #721c24; }')
$null = $Html.AppendLine('.risk-flag { background: #dc3545; color: white; padding: 2px 6px; border-radius: 3px; font-size: 0.8em; font-weight: bold; margin-left: 10px; }')
$null = $Html.AppendLine('.permission-details { font-size: 0.85em; color: #666; margin-top: 8px; font-style: italic; }')
$null = $Html.AppendLine('</style>')
$null = $Html.AppendLine('</head>')
$null = $Html.AppendLine('<body>')
$null = $Html.AppendLine('<div class="container">')

# Header
$null = $Html.AppendLine('<div class="header">')
$null = $Html.AppendLine('<h1>Exchange Mailbox Security Audit Report</h1>')
$null = $Html.AppendLine('<p>Comprehensive Security Assessment and Compliance Analysis</p>')
$null = $Html.AppendLine("<p><strong>Organization:</strong> $($AuditMetadata.OrganizationName) | <strong>Audit Date:</strong> $($AuditMetadata.AuditStartTime) | <strong>Audit ID:</strong> $($AuditMetadata.AuditID)</p>")
$null = $Html.AppendLine('</div>')

# Dashboard
$null = $Html.AppendLine('<div class="dashboard">')
$null = $Html.AppendLine('<div class="card">')
$null = $Html.AppendLine('<h3>Overall Compliance Score</h3>')
$null = $Html.AppendLine("<div class='value' style='color: $(if ($OverallComplianceScore -ge 80) { '#28a745' } elseif ($OverallComplianceScore -ge 60) { '#ffc107' } else { '#dc3545' })'>$OverallComplianceScore%</div>")
$null = $Html.AppendLine('<p>Average compliance across all controls</p>')
$null = $Html.AppendLine('</div>')

$null = $Html.AppendLine('<div class="card">')
$null = $Html.AppendLine('<h3>Controls Assessment</h3>')
$null = $Html.AppendLine("<div class='value'>$TotalControls</div>")
$ControlsText = "Compliant: $CompliantControls | Review Required: $ReviewRequiredControls | Non-Compliant: $NonCompliantControls"
$null = $Html.AppendLine("<p>$ControlsText</p>")
$null = $Html.AppendLine('</div>')

$null = $Html.AppendLine('<div class="card">')
$null = $Html.AppendLine('<h3>Risk Distribution</h3>')
$RiskColor = if ($CriticalRiskControls -gt 0 -or $HighRiskControls -gt 0) { '#dc3545' } elseif ($MediumRiskControls -gt 0) { '#ffc107' } else { '#28a745' }
$RiskLevel = if ($CriticalRiskControls -gt 0) { 'CRITICAL' } elseif ($HighRiskControls -gt 0) { 'HIGH' } elseif ($MediumRiskControls -gt 0) { 'MEDIUM' } else { 'LOW' }
$null = $Html.AppendLine("<div class='value' style='color: $RiskColor'>$RiskLevel</div>")
$RiskText = "Critical: $CriticalRiskControls | High: $HighRiskControls | Medium: $MediumRiskControls | Low: $LowRiskControls"
$null = $Html.AppendLine("<p>$RiskText</p>")
$null = $Html.AppendLine('</div>')
$null = $Html.AppendLine('</div>')

# Control Summary Table
$null = $Html.AppendLine('<h2>Security Control Analysis</h2>')
$null = $Html.AppendLine('<table>')
$null = $Html.AppendLine('<thead>')
$null = $Html.AppendLine('<tr><th>Control ID</th><th>Control Name</th><th>Risk Level</th><th>Compliance Status</th><th>Score</th><th>Finding</th></tr>')
$null = $Html.AppendLine('</thead>')
$null = $Html.AppendLine('<tbody>')

foreach ($Control in $ControlSummaryData) {
    $StatusIcon = switch ($Control.ComplianceStatus.ToLower()) {
        "compliant" { "OK" }
        "review required" { "WARN" }
        "non-compliant" { "FAIL" }
        default { "UNK" }
    }

    $RiskClass = "risk-" + $Control.RiskLevel.ToLower()
    $StatusClass = "status-" + $Control.ComplianceStatus.ToLower().Replace(' ', '-')

    $SafeControlID = ConvertTo-SafeHtml -Text $Control.ControlID
    $SafeCategory = ConvertTo-SafeHtml -Text $Control.Category
    $SafeControlName = ConvertTo-SafeHtml -Text $Control.ControlName
    $SafeFinding = ConvertTo-SafeHtml -Text $Control.Finding

    $ErrorText = ""
    if ($Control.HasError) {
        $SafeErrorMessage = ConvertTo-SafeHtml -Text $Control.ErrorMessage
        $ErrorText = "<br><small style='color: red;'>ERROR: $SafeErrorMessage</small>"
    }

    $null = $Html.AppendLine('<tr>')
    $null = $Html.AppendLine("<td><strong>$SafeCategory</strong><br><small>$SafeControlID</small></td>")
    $null = $Html.AppendLine("<td>$SafeControlName$ErrorText</td>")
    $null = $Html.AppendLine("<td><span class='badge $RiskClass'>$($Control.RiskLevel)</span></td>")
    $null = $Html.AppendLine("<td>$StatusIcon <span class='badge $StatusClass'>$($Control.ComplianceStatus)</span></td>")
    $null = $Html.AppendLine("<td><strong>$($Control.ComplianceScore)%</strong></td>")
    $null = $Html.AppendLine("<td>$SafeFinding</td>")
    $null = $Html.AppendLine('</tr>')
}

$null = $Html.AppendLine('</tbody>')
$null = $Html.AppendLine('</table>')

# ================================================================================
# DETAILED PERMISSION ANALYSIS SECTIONS
# ================================================================================

$null = $Html.AppendLine('<h2>Detailed Permission Analysis</h2>')
$null = $Html.AppendLine('<p>The following sections provide human-readable details of specific permission relationships identified during the audit:</p>')

# Process each permission type
$PermissionSections = @(
    @{
        Title = "Impersonation Rights"
        DataKey = "MBX_1_1_ImpersonationRights"
        PermissionKey = "ImpersonationRights"
        Description = "Users with application impersonation rights can access mailboxes on behalf of other users"
        RiskLevel = "Critical"
    },
    @{
        Title = "Full Access Permissions"
        DataKey = "MBX_2_1_FullAccessPermissions"
        PermissionKey = "FullAccessPermissions"
        Description = "Users with full access can read, modify, and delete items in target mailboxes"
        RiskLevel = "High"
    },
    @{
        Title = "Send-As Permissions"
        DataKey = "MBX_4_1_SendAsPermissions"
        PermissionKey = "SendAsPermissions"
        Description = "Users with Send-As permissions can send emails appearing to come from the target mailbox"
        RiskLevel = "High"
    },
    @{
        Title = "Send-On-Behalf Permissions"
        DataKey = "MBX_5_1_SendOnBehalfPermissions"
        PermissionKey = "SendOnBehalfPermissions"
        Description = "Users with Send-On-Behalf permissions can send emails on behalf of the target mailbox owner"
        RiskLevel = "Medium"
    }
)

foreach ($Section in $PermissionSections) {
    if ($AuditData.PSObject.Properties.Name -contains $Section.DataKey) {
        $SectionData = $AuditData.($Section.DataKey)
        $Permissions = $SectionData.($Section.PermissionKey)

        if ($Permissions -and $Permissions.Count -gt 0) {
            # Section Header
            $RiskColor = switch ($Section.RiskLevel.ToLower()) {
                "critical" { "#dc3545" }
                "high" { "#fd7e14" }
                "medium" { "#ffc107" }
                "low" { "#28a745" }
                default { "#6c757d" }
            }

            $null = $Html.AppendLine('<div class="permission-section">')
            $null = $Html.AppendLine("<h3 style='color: $RiskColor; border-bottom: 2px solid $RiskColor; padding-bottom: 10px;'>")
            $null = $Html.AppendLine("$($Section.Title) <span class='badge' style='background-color: $RiskColor; color: white; font-size: 0.8em;'>$($Permissions.Count) permissions</span>")
            $null = $Html.AppendLine('</h3>')
            $null = $Html.AppendLine("<p style='color: #666; font-style: italic; margin-bottom: 20px;'>$($Section.Description)</p>")

            # Permission List
            $null = $Html.AppendLine('<div class="permission-list">')

            foreach ($Permission in $Permissions) {
                # Determine risk indicators
                $RiskIndicators = @()
                $CrossDepartment = $false

                if ($Permission.PSObject.Properties.Name -contains "UserDepartment" -and
                    $Permission.PSObject.Properties.Name -contains "Department" -and
                    $Permission.UserDepartment -ne $Permission.Department) {
                    $CrossDepartment = $true
                    $RiskIndicators += "Cross-Department"
                }

                if ($Permission.PSObject.Properties.Name -contains "IsInherited" -and $Permission.IsInherited -eq $false) {
                    $RiskIndicators += "Direct Assignment"
                }

                if ($Permission.PSObject.Properties.Name -contains "BusinessJustification" -and
                    [string]::IsNullOrWhiteSpace($Permission.BusinessJustification)) {
                    $RiskIndicators += "No Business Justification"
                }

                # Build human-readable description with enhanced formatting
                $UserDisplayName = if ($Permission.PSObject.Properties.Name -contains "UserDisplayName") { $Permission.UserDisplayName } else { $Permission.User -replace ".*\\", "" }
                $UserEmail = if ($Permission.PSObject.Properties.Name -contains "UserPrimarySmtpAddress") { $Permission.UserPrimarySmtpAddress } else { "N/A" }
                $MailboxDisplayName = $Permission.MailboxDisplayName
                $MailboxEmail = $Permission.MailboxPrimarySmtpAddress

                # Format user and mailbox names with contextual identifiers
                $FormattedUserName = Format-UserDisplayName -UserDisplayName $UserDisplayName -UserEmail $UserEmail -UserAccount $Permission.User
                $FormattedMailboxName = Format-MailboxDisplayName -MailboxDisplayName $MailboxDisplayName -MailboxEmail $MailboxEmail

                $PermissionDescription = switch ($Section.Title) {
                    "Impersonation Rights" { "$FormattedUserName has impersonation rights on $FormattedMailboxName" }
                    "Full Access Permissions" { "$FormattedUserName has full access to $FormattedMailboxName" }
                    "Send-As Permissions" { "$FormattedUserName can send as $FormattedMailboxName" }
                    "Send-On-Behalf Permissions" { "$FormattedUserName can send on behalf of $FormattedMailboxName" }
                    default { "$FormattedUserName has $($Permission.AccessRights) permission on $FormattedMailboxName" }
                }

                # Risk indicator styling
                $RiskClass = if ($CrossDepartment) { "cross-dept-risk" } else { "same-dept" }

                $null = $Html.AppendLine('<div class="permission-item">')
                $null = $Html.AppendLine("<div class='permission-description'>$PermissionDescription</div>")

                # Department information
                if ($Permission.PSObject.Properties.Name -contains "UserDepartment" -and
                    $Permission.PSObject.Properties.Name -contains "Department") {
                    $DeptInfo = "User Department: $($Permission.UserDepartment) | Target Department: $($Permission.Department)"
                    if ($CrossDepartment) {
                        $null = $Html.AppendLine("<div class='dept-info cross-dept'>$DeptInfo <span class='risk-flag'>CROSS-DEPARTMENT</span></div>")
                    } else {
                        $null = $Html.AppendLine("<div class='dept-info same-dept'>$DeptInfo</div>")
                    }
                }

                # Additional details
                $Details = @()
                if ($Permission.PSObject.Properties.Name -contains "AssignmentDate") {
                    $Details += "Assigned: $($Permission.AssignmentDate)"
                }
                if ($Permission.PSObject.Properties.Name -contains "LastAccessDate") {
                    $Details += "Last Access: $($Permission.LastAccessDate)"
                }
                if ($Permission.PSObject.Properties.Name -contains "BusinessJustification" -and
                    -not [string]::IsNullOrWhiteSpace($Permission.BusinessJustification)) {
                    $Details += "Justification: $($Permission.BusinessJustification)"
                }

                if ($Details.Count -gt 0) {
                    $null = $Html.AppendLine("<div class='permission-details'>$($Details -join ' | ')</div>")
                }

                $null = $Html.AppendLine('</div>')
            }

            $null = $Html.AppendLine('</div>')
            $null = $Html.AppendLine('</div>')
        }
    }
}

# Footer
$ReportEndTime = Get-Date
$ReportDuration = $ReportEndTime - $ReportStartTime

$null = $Html.AppendLine('<div class="footer">')
$null = $Html.AppendLine('<p><strong>Exchange Mailbox Security Audit Report</strong></p>')
$null = $Html.AppendLine('<p>Generated by E.Z. Consultancy Exchange Audit Report Generator (Test Version)</p>')
$null = $Html.AppendLine('<p>Report generated using PowerShell script (Audit-Report-Generator.ps1) designed specially for this purpose</p>')
$null = $Html.AppendLine("<p>Report Generated: $($ReportEndTime.ToString('yyyy-MM-dd HH:mm:ss')) | Duration: $([math]::Round($ReportDuration.TotalSeconds, 2)) seconds</p>")
$null = $Html.AppendLine("<p>Report ID: $ReportID | Source Audit ID: $($AuditMetadata.AuditID)</p>")
$null = $Html.AppendLine('</div>')

$null = $Html.AppendLine('</div>')
$null = $Html.AppendLine('</body>')
$null = $Html.AppendLine('</html>')

# Save HTML report
try {
    $Html.ToString() | Out-File -FilePath $OutputPath -Encoding UTF8 -ErrorAction Stop
    Write-Host "  HTML report generated successfully: $OutputPath" -ForegroundColor Green
}
catch {
    Write-Host "[ERROR] Failed to generate HTML report: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Console output if requested
if ($ConsoleOutput) {
    Write-Host ""
    Write-Host "=== CONSOLE SUMMARY ===" -ForegroundColor Yellow
    Write-Host "Organization: $($AuditMetadata.OrganizationName)" -ForegroundColor White
    Write-Host "Overall Compliance Score: $OverallComplianceScore%" -ForegroundColor $(if ($OverallComplianceScore -ge 80) { "Green" } elseif ($OverallComplianceScore -ge 60) { "Yellow" } else { "Red" })
    Write-Host "Total Controls: $TotalControls | Compliant: $CompliantControls | Review Required: $ReviewRequiredControls" -ForegroundColor White
    Write-Host "Risk Distribution: Critical: $CriticalRiskControls | High: $HighRiskControls | Medium: $MediumRiskControls | Low: $LowRiskControls" -ForegroundColor White
    Write-Host "========================" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Report Generation Completed Successfully" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "HTML Report: $OutputPath" -ForegroundColor Cyan
Write-Host "Report ID: $ReportID" -ForegroundColor Cyan
Write-Host "=================================================================================" -ForegroundColor Green

# Launch browser
try {
    Write-Host ""
    Write-Host "Opening report in default web browser..." -ForegroundColor Yellow
    Start-Process $OutputPath
}
catch {
    Write-Host "[WARNING] Could not open browser automatically: $($_.Exception.Message)" -ForegroundColor Yellow
    Write-Host "Please manually open: $OutputPath" -ForegroundColor Yellow
}
