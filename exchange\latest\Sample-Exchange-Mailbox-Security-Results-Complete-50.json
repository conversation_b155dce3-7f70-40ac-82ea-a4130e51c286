{"AuditMetadata": {"AuditID": "f7e8d9c0-1234-5678-9abc-def012345678", "OrganizationName": "Global Enterprises Inc.", "ExchangeVersion": "Exchange Server 2019 CU12", "AuditStartTime": "2025-09-11 09:00:00", "AuditEndTime": "2025-09-11 09:45:32", "AuditDuration": "00:45:32", "ScriptVersion": "1.6.6", "ExecutedBy": "GLOBALENT\\svc-audit", "ExecutionHost": "EXCH-AUDIT-01.globalent.com", "TotalMailboxesScanned": 50, "DomainController": "DC01.globalent.com", "ForestFunctionalLevel": "Windows2016Forest", "ExchangeOrganization": "Global Enterprises Exchange Organization", "DatabasesAnalyzed": ["DB01", "DB02", "DB03", "DB04"], "AuditScope": "Full Organization Assessment", "ComplianceFramework": "ISO 27001, SOX, GDPR", "ReportingPeriod": "Q3 2025"}, "ComplianceSummary": {"TotalControlsAssessed": 5, "ControlsCompliant": 3, "ControlsReviewRequired": 1, "ControlsNonCompliant": 1, "ControlsWithErrors": 0, "OverallComplianceScore": 82.4, "AssessmentSuccessRate": 100.0, "CriticalFindings": 1, "HighRiskFindings": 2, "MediumRiskFindings": 1, "LowRiskFindings": 1, "TotalPermissionsAnalyzed": 203, "CrossDomainPermissions": 12, "PrivilegedAccountsIdentified": 8, "AssessmentDate": "2025-09-11 09:45:32"}, "MBX_1_1_ImpersonationRights": {"ControlID": "MBX-1.1", "ControlName": "Mailbox Impersonation Rights", "RiskLevel": "Critical", "CVSSScore": 8.5, "ComplianceStatus": "Review Required", "ComplianceScore": 75, "SampleSize": 50, "TotalImpersonationRights": 8, "Finding": "Limited impersonation rights detected - requires review of business justification", "Recommendation": "Review all impersonation assignments for business necessity and implement time-bound access where appropriate", "ImpersonationRights": [{"MailboxDisplayName": "Exchange Service Account", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "GLOBALENT\\svc-backup", "AccessRights": "ApplicationImpersonation", "IsInherited": false, "AssignmentDate": "2025-01-15", "BusinessJustification": "Backup and archival operations"}, {"MailboxDisplayName": "IT Admin Mailbox", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "GLOBALENT\\admin-exchange", "AccessRights": "ApplicationImpersonation", "IsInherited": false, "AssignmentDate": "2025-02-20", "BusinessJustification": "Exchange administration and troubleshooting"}, {"MailboxDisplayName": "Compliance Officer", "MailboxPrimarySmtpAddress": "<EMAIL>", "User": "GLOBALENT\\svc-compliance", "AccessRights": "ApplicationImpersonation", "IsInherited": false, "AssignmentDate": "2025-03-10", "BusinessJustification": "Legal hold and eDiscovery operations"}]}, "MBX_2_1_FullAccessPermissions": {"ControlID": "MBX-2.1", "ControlName": "Mailbox Full Access Permissions", "RiskLevel": "High", "CVSSScore": 7.2, "ComplianceStatus": "Review Required", "ComplianceScore": 78, "SampleSize": 50, "TotalFullAccessPermissions": 120, "UniqueUsersWithFullAccess": 35, "Finding": "Moderate number of Full Access permissions - some cross-departmental access requires review", "Recommendation": "Implement quarterly access reviews and document business justification for all cross-departmental permissions", "FullAccessPermissions": [{"MailboxDisplayName": "<PERSON>", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "UserMailbox", "Department": "Finance", "User": "GLOBALENT\\mary.johnson", "UserDisplayName": "<PERSON>", "UserDepartment": "Finance", "AccessRights": "FullAccess", "IsInherited": false, "AssignmentDate": "2025-01-15", "LastAccessDate": "2025-09-10"}, {"MailboxDisplayName": "HR Shared Mailbox", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "SharedMailbox", "Department": "Human Resources", "User": "GLOBALENT\\sarah.wilson", "UserDisplayName": "<PERSON>", "UserDepartment": "Human Resources", "AccessRights": "FullAccess", "IsInherited": false, "AssignmentDate": "2025-02-01", "LastAccessDate": "2025-09-11"}, {"MailboxDisplayName": "Finance Team Mailbox", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "SharedMailbox", "Department": "Finance", "User": "GLOBALENT\\robert.davis", "UserDisplayName": "<PERSON>", "UserDepartment": "Finance", "AccessRights": "FullAccess", "IsInherited": false, "AssignmentDate": "2025-01-20", "LastAccessDate": "2025-09-09"}, {"MailboxDisplayName": "CEO Executive Assistant", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "UserMailbox", "Department": "Executive", "User": "GLOBALENT\\lisa.brown", "UserDisplayName": "<PERSON>", "UserDepartment": "Executive", "AccessRights": "FullAccess", "IsInherited": false, "AssignmentDate": "2025-01-10", "LastAccessDate": "2025-09-11"}, {"MailboxDisplayName": "IT Support Mailbox", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "SharedMailbox", "Department": "Information Technology", "User": "GLOBALENT\\mike.anderson", "UserDisplayName": "<PERSON>", "UserDepartment": "Information Technology", "AccessRights": "FullAccess", "IsInherited": false, "AssignmentDate": "2025-02-15", "LastAccessDate": "2025-09-10"}, {"MailboxDisplayName": "Sales Manager <PERSON>", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "UserMailbox", "Department": "Sales", "User": "GLOBALENT\\jennifer.garcia", "UserDisplayName": "<PERSON>", "UserDepartment": "Sales", "AccessRights": "FullAccess", "IsInherited": false, "AssignmentDate": "2025-03-01", "LastAccessDate": "2025-09-11"}, {"MailboxDisplayName": "Legal Department", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "SharedMailbox", "Department": "Legal", "User": "GLOBALENT\\amanda.taylor", "UserDisplayName": "<PERSON>", "UserDepartment": "Legal", "AccessRights": "FullAccess", "IsInherited": false, "AssignmentDate": "2025-01-30", "LastAccessDate": "2025-09-09"}, {"MailboxDisplayName": "Conference Room A", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "RoomMailbox", "Department": "Facilities", "User": "GLOBALENT\\facilities.admin", "UserDisplayName": "Facilities Admin", "UserDepartment": "Facilities", "AccessRights": "FullAccess", "IsInherited": false, "AssignmentDate": "2025-01-05", "LastAccessDate": "2025-09-11"}, {"MailboxDisplayName": "Marketing Director", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "UserMailbox", "Department": "Marketing", "User": "GLOBALENT\\david.martinez", "UserDisplayName": "<PERSON>", "UserDepartment": "Marketing", "AccessRights": "FullAccess", "IsInherited": false, "AssignmentDate": "2025-02-20", "LastAccessDate": "2025-09-10"}, {"MailboxDisplayName": "Procurement Team", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "SharedMailbox", "Department": "Procurement", "User": "GLOBALENT\\thomas.white", "UserDisplayName": "<PERSON>", "UserDepartment": "Procurement", "AccessRights": "FullAccess", "IsInherited": false, "AssignmentDate": "2025-03-05", "LastAccessDate": "2025-09-08"}, {"MailboxDisplayName": "Quality Assurance", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "SharedMailbox", "Department": "Quality", "User": "GLOBALENT\\patricia.clark", "UserDisplayName": "<PERSON>", "UserDepartment": "Quality", "AccessRights": "FullAccess", "IsInherited": false, "AssignmentDate": "2025-02-28", "LastAccessDate": "2025-09-11"}, {"MailboxDisplayName": "Customer Service", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "SharedMailbox", "Department": "Customer Service", "User": "GLOBALENT\\nancy.rodriguez", "UserDisplayName": "<PERSON>", "UserDepartment": "Customer Service", "AccessRights": "FullAccess", "IsInherited": false, "AssignmentDate": "2025-01-18", "LastAccessDate": "2025-09-11"}, {"MailboxDisplayName": "Research & Development", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "SharedMailbox", "Department": "Research", "User": "GLOBALENT\\kevin.lewis", "UserDisplayName": "<PERSON>", "UserDepartment": "Research", "AccessRights": "FullAccess", "IsInherited": false, "AssignmentDate": "2025-02-12", "LastAccessDate": "2025-09-10"}, {"MailboxDisplayName": "Training Department", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "SharedMailbox", "Department": "Training", "User": "GLOBALENT\\stephanie.walker", "UserDisplayName": "<PERSON>", "UserDepartment": "Training", "AccessRights": "FullAccess", "IsInherited": false, "AssignmentDate": "2025-03-08", "LastAccessDate": "2025-09-09"}, {"MailboxDisplayName": "Security Team", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "SharedMailbox", "Department": "Security", "User": "GLOBALENT\\brian.hall", "UserDisplayName": "<PERSON>", "UserDepartment": "Security", "AccessRights": "FullAccess", "IsInherited": false, "AssignmentDate": "2025-01-22", "LastAccessDate": "2025-09-11"}, {"MailboxDisplayName": "Operations Manager", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "UserMailbox", "Department": "Operations", "User": "GLOBALENT\\michelle.young", "UserDisplayName": "<PERSON>", "UserDepartment": "Operations", "AccessRights": "FullAccess", "IsInherited": false, "AssignmentDate": "2025-02-25", "LastAccessDate": "2025-09-10"}, {"MailboxDisplayName": "Accounting Department", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "SharedMailbox", "Department": "Accounting", "User": "GLOBALENT\\daniel.king", "UserDisplayName": "<PERSON>", "UserDepartment": "Accounting", "AccessRights": "FullAccess", "IsInherited": false, "AssignmentDate": "2025-01-28", "LastAccessDate": "2025-09-09"}, {"MailboxDisplayName": "Project Management Office", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "SharedMailbox", "Department": "PMO", "User": "GLOBALENT\\carol.wright", "UserDisplayName": "<PERSON>", "UserDepartment": "PMO", "AccessRights": "FullAccess", "IsInherited": false, "AssignmentDate": "2025-03-02", "LastAccessDate": "2025-09-11"}]}, "MBX_3_1_AuditLogging": {"ControlID": "MBX-3.1", "ControlName": "Mailbox Audit Logging Configuration", "RiskLevel": "Medium", "CVSSScore": 5.8, "ComplianceStatus": "Compliant", "ComplianceScore": 95, "SampleSize": 50, "AuditLoggingEnabled": 48, "AuditLoggingDisabled": 2, "Finding": "Excellent audit logging coverage - 96% of mailboxes have audit logging enabled", "Recommendation": "Enable audit logging for remaining 2 mailboxes and maintain current configuration", "AuditConfiguration": {"AdminAuditLogEnabled": true, "AdminAuditLogCmdlets": ["*"], "AdminAuditLogParameters": ["*"], "MailboxAuditBypassEnabled": false, "DefaultAuditSet": "<PERSON><PERSON>,<PERSON><PERSON>ate,Owner", "AuditLogAgeLimit": "90.00:00:00", "AdminAuditLogAgeLimit": "90.00:00:00"}, "NonCompliantMailboxes": [{"MailboxDisplayName": "Test Mailbox 1", "MailboxPrimarySmtpAddress": "<EMAIL>", "AuditEnabled": false, "Reason": "Disabled for testing purposes"}, {"MailboxDisplayName": "Legacy Service Account", "MailboxPrimarySmtpAddress": "<EMAIL>", "AuditEnabled": false, "Reason": "Legacy system compatibility"}]}, "MBX_4_1_SendAsPermissions": {"ControlID": "MBX-4.1", "ControlName": "Send-As Permissions", "RiskLevel": "High", "CVSSScore": 7.0, "ComplianceStatus": "Non-Compliant", "ComplianceScore": 65, "SampleSize": 50, "TotalSendAsPermissions": 45, "UniqueUsersWithSendAs": 25, "Finding": "Elevated number of Send-As permissions detected - some assignments lack proper documentation", "Recommendation": "Implement formal approval process for Send-As permissions and conduct quarterly reviews", "SendAsPermissions": [{"MailboxDisplayName": "CEO <PERSON>", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "UserMailbox", "Department": "Executive", "User": "GLOBALENT\\lisa.brown", "UserDisplayName": "<PERSON>", "UserDepartment": "Executive", "AccessRights": "Send-As", "IsInherited": false, "AssignmentDate": "2025-01-05", "BusinessJustification": "Executive assistant delegation"}, {"MailboxDisplayName": "Sales Team Mailbox", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "SharedMailbox", "Department": "Sales", "User": "GLOBALENT\\jennifer.garcia", "UserDisplayName": "<PERSON>", "UserDepartment": "Sales", "AccessRights": "Send-As", "IsInherited": false, "AssignmentDate": "2025-02-10", "BusinessJustification": "Sales team coordination"}, {"MailboxDisplayName": "Marketing Communications", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "SharedMailbox", "Department": "Marketing", "User": "GLOBALENT\\david.martinez", "UserDisplayName": "<PERSON>", "UserDepartment": "Marketing", "AccessRights": "Send-As", "IsInherited": false, "AssignmentDate": "2025-01-25", "BusinessJustification": "Marketing campaign management"}, {"MailboxDisplayName": "CFO <PERSON>", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "UserMailbox", "Department": "Finance", "User": "GLOBALENT\\robert.davis", "UserDisplayName": "<PERSON>", "UserDepartment": "Finance", "AccessRights": "Send-As", "IsInherited": false, "AssignmentDate": "2025-01-08", "BusinessJustification": "Financial communications delegation"}, {"MailboxDisplayName": "Customer Service", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "SharedMailbox", "Department": "Customer Service", "User": "GLOBALENT\\nancy.rodriguez", "UserDisplayName": "<PERSON>", "UserDepartment": "Customer Service", "AccessRights": "Send-As", "IsInherited": false, "AssignmentDate": "2025-02-18", "BusinessJustification": "Customer service team lead"}, {"MailboxDisplayName": "Legal Department", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "SharedMailbox", "Department": "Legal", "User": "GLOBALENT\\amanda.taylor", "UserDisplayName": "<PERSON>", "UserDepartment": "Legal", "AccessRights": "Send-As", "IsInherited": false, "AssignmentDate": "2025-02-01", "BusinessJustification": "Legal counsel communications"}, {"MailboxDisplayName": "IT Support Mailbox", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "SharedMailbox", "Department": "Information Technology", "User": "GLOBALENT\\mike.anderson", "UserDisplayName": "<PERSON>", "UserDepartment": "Information Technology", "AccessRights": "Send-As", "IsInherited": false, "AssignmentDate": "2025-02-22", "BusinessJustification": "IT support team coordination"}, {"MailboxDisplayName": "Training Department", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "SharedMailbox", "Department": "Training", "User": "GLOBALENT\\stephanie.walker", "UserDisplayName": "<PERSON>", "UserDepartment": "Training", "AccessRights": "Send-As", "IsInherited": false, "AssignmentDate": "2025-03-10", "BusinessJustification": "Training program communications"}, {"MailboxDisplayName": "Procurement Team", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "SharedMailbox", "Department": "Procurement", "User": "GLOBALENT\\thomas.white", "UserDisplayName": "<PERSON>", "UserDepartment": "Procurement", "AccessRights": "Send-As", "IsInherited": false, "AssignmentDate": "2025-03-07", "BusinessJustification": "Vendor communications"}, {"MailboxDisplayName": "Quality Assurance", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "SharedMailbox", "Department": "Quality", "User": "GLOBALENT\\patricia.clark", "UserDisplayName": "<PERSON>", "UserDepartment": "Quality", "AccessRights": "Send-As", "IsInherited": false, "AssignmentDate": "2025-03-01", "BusinessJustification": "Quality assurance reporting"}]}, "MBX_5_1_SendOnBehalfPermissions": {"ControlID": "MBX-5.1", "ControlName": "Send-On-Behalf Permissions", "RiskLevel": "Low", "CVSSScore": 4.2, "ComplianceStatus": "Compliant", "ComplianceScore": 88, "SampleSize": 50, "TotalSendOnBehalfPermissions": 38, "UniqueUsersWithSendOnBehalf": 22, "Finding": "Send-On-Behalf permissions are well-managed and appropriately documented", "Recommendation": "Continue current management practices and maintain documentation standards", "SendOnBehalfPermissions": [{"MailboxDisplayName": "CFO <PERSON>", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "UserMailbox", "Department": "Finance", "User": "GLOBALENT\\robert.davis", "UserDisplayName": "<PERSON>", "UserDepartment": "Finance", "AccessRights": "Send-On-Behalf", "IsInherited": false, "AssignmentDate": "2025-01-12", "BusinessJustification": "Financial reporting and communications"}, {"MailboxDisplayName": "HR Director", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "UserMailbox", "Department": "Human Resources", "User": "GLOBALENT\\sarah.wilson", "UserDisplayName": "<PERSON>", "UserDepartment": "Human Resources", "AccessRights": "Send-On-Behalf", "IsInherited": false, "AssignmentDate": "2025-02-05", "BusinessJustification": "HR policy communications"}, {"MailboxDisplayName": "Operations Manager", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "UserMailbox", "Department": "Operations", "User": "GLOBALENT\\michelle.young", "UserDisplayName": "<PERSON>", "UserDepartment": "Operations", "AccessRights": "Send-On-Behalf", "IsInherited": false, "AssignmentDate": "2025-02-28", "BusinessJustification": "Operations coordination"}, {"MailboxDisplayName": "Sales Manager <PERSON>", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "UserMailbox", "Department": "Sales", "User": "GLOBALENT\\jennifer.garcia", "UserDisplayName": "<PERSON>", "UserDepartment": "Sales", "AccessRights": "Send-On-Behalf", "IsInherited": false, "AssignmentDate": "2025-03-03", "BusinessJustification": "Regional sales management"}, {"MailboxDisplayName": "Marketing Director", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "UserMailbox", "Department": "Marketing", "User": "GLOBALENT\\david.martinez", "UserDisplayName": "<PERSON>", "UserDepartment": "Marketing", "AccessRights": "Send-On-Behalf", "IsInherited": false, "AssignmentDate": "2025-02-22", "BusinessJustification": "Marketing strategy communications"}, {"MailboxDisplayName": "Project Management Office", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "SharedMailbox", "Department": "PMO", "User": "GLOBALENT\\carol.wright", "UserDisplayName": "<PERSON>", "UserDepartment": "PMO", "AccessRights": "Send-On-Behalf", "IsInherited": false, "AssignmentDate": "2025-03-04", "BusinessJustification": "Project coordination"}, {"MailboxDisplayName": "Research & Development", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "SharedMailbox", "Department": "Research", "User": "GLOBALENT\\kevin.lewis", "UserDisplayName": "<PERSON>", "UserDepartment": "Research", "AccessRights": "Send-On-Behalf", "IsInherited": false, "AssignmentDate": "2025-02-14", "BusinessJustification": "Research project communications"}, {"MailboxDisplayName": "Security Team", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "SharedMailbox", "Department": "Security", "User": "GLOBALENT\\brian.hall", "UserDisplayName": "<PERSON>", "UserDepartment": "Security", "AccessRights": "Send-On-Behalf", "IsInherited": false, "AssignmentDate": "2025-01-25", "BusinessJustification": "Security incident communications"}, {"MailboxDisplayName": "Accounting Department", "MailboxPrimarySmtpAddress": "<EMAIL>", "MailboxType": "SharedMailbox", "Department": "Accounting", "User": "GLOBALENT\\daniel.king", "UserDisplayName": "<PERSON>", "UserDepartment": "Accounting", "AccessRights": "Send-On-Behalf", "IsInherited": false, "AssignmentDate": "2025-01-30", "BusinessJustification": "Accounting team coordination"}]}, "AdministratorDiscovery": {"TotalAdministrators": 8, "ExchangeAdministrators": 5, "DomainAdministrators": 3, "EnterpriseAdministrators": 2, "OrganizationManagement": 4, "Finding": "Administrator accounts are properly segregated with appropriate role assignments", "Recommendation": "Continue regular review of administrative privileges and implement just-in-time access where possible", "Administrators": [{"UserName": "GLOBALENT\\admin-exchange", "DisplayName": "Exchange Administrator", "Department": "Information Technology", "Roles": ["Organization Management", "Server Management"], "LastLogon": "2025-09-11 08:30:00", "AccountEnabled": true, "PasswordLastSet": "2025-08-15 14:20:00", "AccountCreated": "2024-12-01 10:00:00"}, {"UserName": "GLOBALENT\\admin-domain", "DisplayName": "Domain Administrator", "Department": "Information Technology", "Roles": ["Domain Admins", "Enterprise Admins"], "LastLogon": "2025-09-10 16:45:00", "AccountEnabled": true, "PasswordLastSet": "2025-08-20 09:15:00", "AccountCreated": "2024-11-15 12:30:00"}, {"UserName": "GLOBALENT\\svc-backup", "DisplayName": "Backup Service Account", "Department": "Information Technology", "Roles": ["Backup Operators", "Exchange Trusted Subsystem"], "LastLogon": "2025-09-11 02:00:00", "AccountEnabled": true, "PasswordLastSet": "2025-07-01 00:00:00", "AccountCreated": "2024-10-01 08:00:00"}, {"UserName": "GLOBALENT\\svc-compliance", "DisplayName": "Compliance Service Account", "Department": "Legal", "Roles": ["Discovery Management", "Records Management"], "LastLogon": "2025-09-11 07:00:00", "AccountEnabled": true, "PasswordLastSet": "2025-08-01 12:00:00", "AccountCreated": "2025-01-01 09:00:00"}]}, "CrossDomainAnalysis": {"TotalCrossDomainPermissions": 12, "TrustedDomains": ["PARTNER.COM", "SUBSIDIARY.NET"], "ExternalUserAccess": 8, "Finding": "Limited cross-domain permissions with proper trust relationships", "Recommendation": "Maintain current cross-domain security policies and review external access quarterly", "CrossDomainPermissions": [{"MailboxDisplayName": "Shared Project Alpha", "MailboxPrimarySmtpAddress": "<EMAIL>", "ExternalUser": "PARTNER\\john.external", "ExternalDomain": "PARTNER.COM", "AccessType": "FullAccess", "BusinessJustification": "Joint venture project collaboration", "ApprovalDate": "2025-03-01", "ExpirationDate": "2025-12-31"}, {"MailboxDisplayName": "Vendor Communications", "MailboxPrimarySmtpAddress": "<EMAIL>", "ExternalUser": "SUBSIDIARY\\mary.vendor", "ExternalDomain": "SUBSIDIARY.NET", "AccessType": "Send-As", "BusinessJustification": "Subsidiary coordination", "ApprovalDate": "2025-02-15", "ExpirationDate": "2026-02-15"}]}}