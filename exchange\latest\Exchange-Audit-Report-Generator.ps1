#Requires -Version 5.1

# ================================================================================
# **Created By:** E.Z. Consultancy
# **Script Version:** 1.0.0
# **Exchange Version:** Exchange Server 2016/2019/Online Compatible
# 🏛️ **Authority:** Internal Audit
# ================================================================================

<#
================================================================================
Exchange Mailbox Security Audit Report Generator
================================================================================
Description: Processes JSON output from Exchange-Mailbox-Security-Audit.ps1 and 
generates comprehensive, human-readable audit reports in HTML format.

VERSION HISTORY:
================================================================================
Version 1.0.0 - September 11, 2025 - Initial Release
- Author: E.Z. Consultancy Development Team
- HTML report generation with professional styling
- Executive dashboard with compliance metrics
- Detailed security control analysis
- Permission analysis tables with risk assessment
- Cross-domain relationship mapping
- Administrator discovery reporting
- Actionable recommendations prioritization
- CSV export capabilities for detailed analysis
- Compatible with Exchange-Mailbox-Security-Audit.ps1 v1.6.6

================================================================================

Current Version: 1.0.0 (Exchange 2016/2019/Online Compatible)
Created: September 11, 2025
Last Updated: September 11, 2025

INSTRUCTIONS FOR ADMIN:
1. Ensure you have the JSON output file from Exchange-Mailbox-Security-Audit.ps1
2. Run PowerShell as Administrator and execute:

   # Process latest JSON file (automatic detection)
   .\Exchange-Audit-Report-Generator.ps1

   # Process specific JSON file
   .\Exchange-Audit-Report-Generator.ps1 -JsonFilePath "C:\Path\To\Results.json"

   # Generate report with custom output location
   .\Exchange-Audit-Report-Generator.ps1 -OutputPath "C:\Reports\Audit-Report.html"

   # Filter by risk level
   .\Exchange-Audit-Report-Generator.ps1 -RiskFilter "High,Critical"

   # Export CSV files for detailed analysis
   .\Exchange-Audit-Report-Generator.ps1 -ExportCsv

3. Open the generated HTML report in your web browser
4. Use CSV exports for detailed data analysis in Excel

CRITICAL: This script is 100% READ-ONLY and processes existing audit data
No Exchange configuration modifications are performed
================================================================================
#>

[CmdletBinding()]
param(
    [Parameter(Mandatory = $false)]
    [string]$JsonFilePath = "",

    [Parameter(Mandatory = $false)]
    [string]$OutputPath = "",

    [Parameter(Mandatory = $false)]
    [string[]]$RiskFilter = @(),

    [Parameter(Mandatory = $false)]
    [string[]]$ControlFilter = @(),

    [Parameter(Mandatory = $false)]
    [string[]]$DomainFilter = @(),

    [Parameter(Mandatory = $false)]
    [switch]$ExportCsv,

    [Parameter(Mandatory = $false)]
    [switch]$ConsoleOutput,

    [Parameter(Mandatory = $false)]
    [switch]$SkipBrowser
)

# ================================================================================
# POWERSHELL VERSION COMPATIBILITY CHECK
# ================================================================================
if ($PSVersionTable.PSVersion.Major -lt 5) {
    Write-Host "[ERROR] This script requires PowerShell 5.1 or higher. Current version: $($PSVersionTable.PSVersion)" -ForegroundColor Red
    Write-Host "Please upgrade PowerShell or run on a compatible system." -ForegroundColor Red
    exit 1
}

if ($PSVersionTable.PSVersion.Major -eq 5 -and $PSVersionTable.PSVersion.Minor -eq 0) {
    Write-Host "[WARNING] PowerShell 5.0 detected. PowerShell 5.1 or higher is recommended for optimal performance." -ForegroundColor Yellow
}

Write-Host "[INFO] PowerShell Version: $($PSVersionTable.PSVersion) - Compatible" -ForegroundColor Green

# Initialize report generation session
$ReportStartTime = Get-Date
$ReportID = [System.Guid]::NewGuid()

# ================================================================================
# SCRIPT VERSION AND ENVIRONMENT INFORMATION
# ================================================================================
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Exchange Mailbox Security Audit Report Generator" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Script Version: 1.0.0 (September 11, 2025)" -ForegroundColor White
Write-Host "Created By: E.Z. Consultancy" -ForegroundColor White
Write-Host "PowerShell Version: $($PSVersionTable.PSVersion)" -ForegroundColor White
Write-Host "PowerShell Edition: $($PSVersionTable.PSEdition)" -ForegroundColor White
Write-Host "Execution Host: $($env:COMPUTERNAME)" -ForegroundColor White
Write-Host "Execution User: $($env:USERNAME)" -ForegroundColor White
Write-Host "Execution Time: $($ReportStartTime.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor White
Write-Host "Report ID: $ReportID" -ForegroundColor Yellow
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host ""

# ================================================================================
# HELPER FUNCTIONS
# ================================================================================

function Find-LatestJsonFile {
    param(
        [string]$SearchPath = "."
    )
    
    try {
        $JsonFiles = Get-ChildItem -Path $SearchPath -Filter "Exchange-Mailbox-Security-Results*.json" -ErrorAction SilentlyContinue |
            Sort-Object LastWriteTime -Descending
        
        if ($JsonFiles -and $JsonFiles.Count -gt 0) {
            return $JsonFiles[0].FullName
        }
        
        # Fallback: look for any JSON files with audit-related names
        $FallbackFiles = Get-ChildItem -Path $SearchPath -Filter "*audit*.json" -ErrorAction SilentlyContinue |
            Sort-Object LastWriteTime -Descending
        
        if ($FallbackFiles -and $FallbackFiles.Count -gt 0) {
            Write-Host "[INFO] Using fallback JSON file: $($FallbackFiles[0].Name)" -ForegroundColor Yellow
            return $FallbackFiles[0].FullName
        }
        
        return $null
    }
    catch {
        Write-Host "[ERROR] Failed to search for JSON files: $($_.Exception.Message)" -ForegroundColor Red
        return $null
    }
}

function Get-RiskLevelColor {
    param([string]$RiskLevel)
    
    switch ($RiskLevel.ToLower()) {
        "critical" { return "#dc3545" }  # Red
        "high" { return "#fd7e14" }      # Orange
        "medium" { return "#ffc107" }    # Yellow
        "low" { return "#28a745" }       # Green
        default { return "#6c757d" }     # Gray
    }
}

function Get-ComplianceStatusColor {
    param([string]$Status)
    
    switch ($Status.ToLower()) {
        "compliant" { return "#28a745" }      # Green
        "review required" { return "#fd7e14" } # Orange
        "non-compliant" { return "#dc3545" }   # Red
        "no data" { return "#6c757d" }         # Gray
        default { return "#6c757d" }           # Gray
    }
}

function ConvertTo-SafeHtml {
    param([string]$Text)
    
    if ([string]::IsNullOrWhiteSpace($Text)) {
        return "N/A"
    }
    
    return [System.Web.HttpUtility]::HtmlEncode($Text)
}

function Format-Number {
    param(
        [object]$Number,
        [int]$DecimalPlaces = 0
    )
    
    if ($null -eq $Number -or $Number -eq "Unknown" -or $Number -eq "") {
        return "N/A"
    }
    
    try {
        $NumericValue = [double]$Number
        return $NumericValue.ToString("N$DecimalPlaces")
    }
    catch {
        return $Number.ToString()
    }
}

function Get-ControlSummaryData {
    param([hashtable]$AuditData)
    
    $ControlSummary = @()
    
    # Define control mappings
    $ControlMappings = @{
        "MBX_1_1_ImpersonationRights" = @{
            ID = "MBX-1.1"
            Name = "Mailbox Impersonation Rights"
            Category = "Access Control"
        }
        "MBX_2_1_FullAccessPermissions" = @{
            ID = "MBX-2.1"
            Name = "Full Access Permissions"
            Category = "Delegation Management"
        }
        "MBX_3_1_AuditLogging" = @{
            ID = "MBX-3.1"
            Name = "Audit Logging Configuration"
            Category = "Compliance & Monitoring"
        }
        "MBX_4_1_SendAsPermissions" = @{
            ID = "MBX-4.1"
            Name = "Send-As Permissions"
            Category = "Email Delegation"
        }
        "MBX_5_1_SendOnBehalfPermissions" = @{
            ID = "MBX-5.1"
            Name = "Send-On-Behalf Permissions"
            Category = "Email Delegation"
        }
    }
    
    foreach ($ControlKey in $ControlMappings.Keys) {
        if ($AuditData.ContainsKey($ControlKey)) {
            $ControlData = $AuditData[$ControlKey]
            $Mapping = $ControlMappings[$ControlKey]
            
            $ControlSummary += @{
                ControlID = if ($ControlData.ControlID) { $ControlData.ControlID } else { $Mapping.ID }
                ControlName = if ($ControlData.ControlName) { $ControlData.ControlName } else { $Mapping.Name }
                Category = $Mapping.Category
                RiskLevel = if ($ControlData.RiskLevel) { $ControlData.RiskLevel } else { "Unknown" }
                ComplianceStatus = if ($ControlData.ComplianceStatus) { $ControlData.ComplianceStatus } else { "Unknown" }
                ComplianceScore = if ($ControlData.ComplianceScore) { $ControlData.ComplianceScore } else { 0 }
                CVSSScore = if ($ControlData.CVSSScore) { $ControlData.CVSSScore } else { 0 }
                Finding = if ($ControlData.Finding) { $ControlData.Finding } else { "No findings available" }
                Recommendation = if ($ControlData.Recommendation) { $ControlData.Recommendation } else { "No recommendations available" }
                HasError = $ControlData.ContainsKey("Error")
                ErrorMessage = if ($ControlData.ContainsKey("Error")) { $ControlData.Error } else { "" }
            }
        }
    }
    
    return $ControlSummary
}

# ================================================================================
# INPUT VALIDATION AND FILE PROCESSING
# ================================================================================

Write-Host "Step 1/6: Validating input parameters and locating JSON file..." -ForegroundColor Yellow

# Determine JSON file path
if ([string]::IsNullOrWhiteSpace($JsonFilePath)) {
    Write-Host "  No JSON file specified, searching for latest audit results..." -ForegroundColor Gray
    $JsonFilePath = Find-LatestJsonFile

    if ([string]::IsNullOrWhiteSpace($JsonFilePath)) {
        Write-Host "[ERROR] No JSON audit files found in current directory." -ForegroundColor Red
        Write-Host "Please specify a JSON file path using -JsonFilePath parameter." -ForegroundColor Red
        exit 1
    }

    Write-Host "  Found latest JSON file: $JsonFilePath" -ForegroundColor Green
}

# Validate JSON file exists
if (-not (Test-Path -Path $JsonFilePath)) {
    Write-Host "[ERROR] JSON file not found: $JsonFilePath" -ForegroundColor Red
    exit 1
}

Write-Host "  JSON file validated: $JsonFilePath" -ForegroundColor Green

# Determine output path
if ([string]::IsNullOrWhiteSpace($OutputPath)) {
    $JsonFileName = [System.IO.Path]::GetFileNameWithoutExtension($JsonFilePath)
    $OutputPath = ".\$JsonFileName-Report-$(Get-Date -Format 'yyyyMMdd-HHmmss').html"
}

Write-Host "  Output path: $OutputPath" -ForegroundColor Green

# ================================================================================
# JSON DATA LOADING AND VALIDATION
# ================================================================================

Write-Host "Step 2/6: Loading and validating JSON audit data..." -ForegroundColor Yellow

try {
    $JsonContent = Get-Content -Path $JsonFilePath -Raw -Encoding UTF8
    $AuditData = $JsonContent | ConvertFrom-Json -ErrorAction Stop

    # Convert PSCustomObject to Hashtable for easier processing
    $AuditDataHash = @{}
    $AuditData.PSObject.Properties | ForEach-Object {
        $AuditDataHash[$_.Name] = $_.Value
    }

    Write-Host "  JSON data loaded successfully" -ForegroundColor Green
    Write-Host "  Audit sections found: $($AuditDataHash.Keys.Count)" -ForegroundColor Gray

    # Validate essential sections
    $RequiredSections = @("AuditMetadata", "ComplianceSummary")
    $MissingSections = @()

    foreach ($Section in $RequiredSections) {
        if (-not $AuditDataHash.ContainsKey($Section)) {
            $MissingSections += $Section
        }
    }

    if ($MissingSections.Count -gt 0) {
        Write-Host "[WARNING] Missing required sections: $($MissingSections -join ', ')" -ForegroundColor Yellow
        Write-Host "Report may be incomplete." -ForegroundColor Yellow
    }

    # Extract audit metadata
    $AuditMetadata = if ($AuditDataHash.ContainsKey("AuditMetadata")) { $AuditDataHash["AuditMetadata"] } else { @{} }
    $ComplianceSummary = if ($AuditDataHash.ContainsKey("ComplianceSummary")) { $AuditDataHash["ComplianceSummary"] } else { @{} }

    Write-Host "  Audit ID: $($AuditMetadata.AuditID)" -ForegroundColor Gray
    Write-Host "  Audit Date: $($AuditMetadata.AuditStartTime)" -ForegroundColor Gray
    Write-Host "  Organization: $($AuditMetadata.OrganizationName)" -ForegroundColor Gray

}
catch {
    Write-Host "[ERROR] Failed to load or parse JSON file: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# ================================================================================
# DATA PROCESSING AND ANALYSIS
# ================================================================================

Write-Host "Step 3/6: Processing audit data and generating analysis..." -ForegroundColor Yellow

# Get control summary data
$ControlSummaryData = Get-ControlSummaryData -AuditData $AuditDataHash

# Apply filters if specified
if ($RiskFilter -and $RiskFilter.Count -gt 0) {
    $ControlSummaryData = $ControlSummaryData | Where-Object { $_.RiskLevel -in $RiskFilter }
    Write-Host "  Applied risk filter: $($RiskFilter -join ', ')" -ForegroundColor Gray
}

if ($ControlFilter -and $ControlFilter.Count -gt 0) {
    $ControlSummaryData = $ControlSummaryData | Where-Object { $_.ControlID -in $ControlFilter }
    Write-Host "  Applied control filter: $($ControlFilter -join ', ')" -ForegroundColor Gray
}

# Calculate dashboard metrics
$TotalControls = $ControlSummaryData.Count
$CompliantControls = ($ControlSummaryData | Where-Object { $_.ComplianceStatus -eq "Compliant" }).Count
$ReviewRequiredControls = ($ControlSummaryData | Where-Object { $_.ComplianceStatus -eq "Review Required" }).Count
$NonCompliantControls = ($ControlSummaryData | Where-Object { $_.ComplianceStatus -eq "Non-Compliant" }).Count
$ErrorControls = ($ControlSummaryData | Where-Object { $_.HasError }).Count

$OverallComplianceScore = if ($TotalControls -gt 0) {
    [math]::Round(($ControlSummaryData | Measure-Object -Property ComplianceScore -Average).Average, 1)
} else { 0 }

# Risk level distribution
$CriticalRiskControls = ($ControlSummaryData | Where-Object { $_.RiskLevel -eq "Critical" }).Count
$HighRiskControls = ($ControlSummaryData | Where-Object { $_.RiskLevel -eq "High" }).Count
$MediumRiskControls = ($ControlSummaryData | Where-Object { $_.RiskLevel -eq "Medium" }).Count
$LowRiskControls = ($ControlSummaryData | Where-Object { $_.RiskLevel -eq "Low" }).Count

Write-Host "  Dashboard metrics calculated:" -ForegroundColor Green
Write-Host "    - Total Controls: $TotalControls" -ForegroundColor Gray
Write-Host "    - Compliant: $CompliantControls" -ForegroundColor Gray
Write-Host "    - Review Required: $ReviewRequiredControls" -ForegroundColor Gray
Write-Host "    - Overall Compliance Score: $OverallComplianceScore%" -ForegroundColor Gray

# ================================================================================
# HTML REPORT GENERATION - CSS STYLES
# ================================================================================

Write-Host "Step 4/6: Generating HTML report structure..." -ForegroundColor Yellow

$CssStyles = @"
<style>
    * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
    }

    body {
        font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        line-height: 1.6;
        color: #333;
        background-color: #f8f9fa;
    }

    .container {
        max-width: 1200px;
        margin: 0 auto;
        padding: 20px;
    }

    .header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px;
        border-radius: 10px;
        margin-bottom: 30px;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
    }

    .header h1 {
        font-size: 2.5em;
        margin-bottom: 10px;
        font-weight: 300;
    }

    .header .subtitle {
        font-size: 1.2em;
        opacity: 0.9;
        margin-bottom: 20px;
    }

    .header .metadata {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
        gap: 15px;
        margin-top: 20px;
    }

    .metadata-item {
        background: rgba(255, 255, 255, 0.1);
        padding: 10px 15px;
        border-radius: 5px;
        backdrop-filter: blur(10px);
    }

    .metadata-label {
        font-size: 0.9em;
        opacity: 0.8;
        margin-bottom: 5px;
    }

    .metadata-value {
        font-size: 1.1em;
        font-weight: 500;
    }

    .dashboard {
        display: grid;
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 20px;
        margin-bottom: 30px;
    }

    .dashboard-card {
        background: white;
        padding: 25px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        border-left: 4px solid #667eea;
        transition: transform 0.2s ease;
    }

    .dashboard-card:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    }

    .dashboard-card h3 {
        color: #667eea;
        margin-bottom: 15px;
        font-size: 1.1em;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .dashboard-value {
        font-size: 2.5em;
        font-weight: bold;
        margin-bottom: 10px;
    }

    .dashboard-label {
        color: #666;
        font-size: 0.9em;
    }

    .section {
        background: white;
        margin-bottom: 30px;
        border-radius: 10px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
        overflow: hidden;
    }

    .section-header {
        background: #f8f9fa;
        padding: 20px 30px;
        border-bottom: 1px solid #dee2e6;
    }

    .section-header h2 {
        color: #495057;
        font-size: 1.5em;
        margin-bottom: 5px;
    }

    .section-header .description {
        color: #6c757d;
        font-size: 0.95em;
    }

    .section-content {
        padding: 30px;
    }

    .table-responsive {
        overflow-x: auto;
        margin-bottom: 20px;
    }

    table {
        width: 100%;
        border-collapse: collapse;
        margin-bottom: 20px;
    }

    th, td {
        padding: 12px 15px;
        text-align: left;
        border-bottom: 1px solid #dee2e6;
    }

    th {
        background-color: #f8f9fa;
        font-weight: 600;
        color: #495057;
        text-transform: uppercase;
        font-size: 0.85em;
        letter-spacing: 0.5px;
    }

    tr:hover {
        background-color: #f8f9fa;
    }

    .status-badge {
        display: inline-block;
        padding: 4px 12px;
        border-radius: 20px;
        font-size: 0.8em;
        font-weight: 500;
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    .risk-critical { background-color: #dc3545; color: white; }
    .risk-high { background-color: #fd7e14; color: white; }
    .risk-medium { background-color: #ffc107; color: #212529; }
    .risk-low { background-color: #28a745; color: white; }

    .compliance-compliant { background-color: #d4edda; color: #155724; }
    .compliance-review { background-color: #fff3cd; color: #856404; }
    .compliance-non-compliant { background-color: #f8d7da; color: #721c24; }
    .compliance-no-data { background-color: #e2e3e5; color: #383d41; }

    .progress-bar {
        width: 100%;
        height: 20px;
        background-color: #e9ecef;
        border-radius: 10px;
        overflow: hidden;
        margin: 10px 0;
    }

    .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #28a745 0%, #20c997 50%, #17a2b8 100%);
        transition: width 0.3s ease;
        border-radius: 10px;
    }

    .alert {
        padding: 15px 20px;
        margin-bottom: 20px;
        border-radius: 5px;
        border-left: 4px solid;
    }

    .alert-info {
        background-color: #d1ecf1;
        border-color: #17a2b8;
        color: #0c5460;
    }

    .alert-warning {
        background-color: #fff3cd;
        border-color: #ffc107;
        color: #856404;
    }

    .alert-danger {
        background-color: #f8d7da;
        border-color: #dc3545;
        color: #721c24;
    }

    .recommendations {
        background: #f8f9fa;
        padding: 20px;
        border-radius: 8px;
        margin-top: 20px;
    }

    .recommendations h4 {
        color: #495057;
        margin-bottom: 15px;
        font-size: 1.1em;
    }

    .recommendations ul {
        list-style-type: none;
        padding-left: 0;
    }

    .recommendations li {
        padding: 8px 0;
        border-bottom: 1px solid #dee2e6;
    }

    .recommendations li:last-child {
        border-bottom: none;
    }

    .footer {
        text-align: center;
        padding: 30px;
        color: #6c757d;
        font-size: 0.9em;
        border-top: 1px solid #dee2e6;
        margin-top: 40px;
    }

    @media (max-width: 768px) {
        .container {
            padding: 10px;
        }

        .header {
            padding: 20px;
        }

        .header h1 {
            font-size: 2em;
        }

        .dashboard {
            grid-template-columns: 1fr;
        }

        .section-content {
            padding: 20px;
        }

        table {
            font-size: 0.9em;
        }

        th, td {
            padding: 8px 10px;
        }
    }

    .print-only {
        display: none;
    }

    @media print {
        .print-only {
            display: block;
        }

        .no-print {
            display: none;
        }

        body {
            background: white;
        }

        .section {
            box-shadow: none;
            border: 1px solid #dee2e6;
        }

        .dashboard-card {
            box-shadow: none;
            border: 1px solid #dee2e6;
        }
    }
</style>
"@

# ================================================================================
# HTML GENERATION FUNCTIONS
# ================================================================================

function Generate-HtmlHeader {
    param(
        [hashtable]$AuditMetadata,
        [hashtable]$ComplianceSummary
    )

    $OrganizationName = if ($AuditMetadata.OrganizationName) { $AuditMetadata.OrganizationName } else { "Unknown Organization" }
    $AuditDate = if ($AuditMetadata.AuditStartTime) {
        try {
            [DateTime]::Parse($AuditMetadata.AuditStartTime).ToString("MMMM dd, yyyy 'at' HH:mm")
        } catch {
            $AuditMetadata.AuditStartTime
        }
    } else { "Unknown Date" }
    $AuditID = if ($AuditMetadata.AuditID) { $AuditMetadata.AuditID } else { "Unknown" }
    $ScriptVersion = if ($AuditMetadata.ScriptVersion) { $AuditMetadata.ScriptVersion } else { "Unknown" }
    $TotalMailboxes = if ($AuditMetadata.TotalMailboxes) { Format-Number -Number $AuditMetadata.TotalMailboxes } else { "Unknown" }
    $DomainScope = if ($AuditMetadata.DomainFilterScope) { $AuditMetadata.DomainFilterScope } else { "All domains" }

    return @"
        <div class="header">
            <h1>Exchange Mailbox Security Audit Report</h1>
            <div class="subtitle">Comprehensive Security Assessment and Compliance Analysis</div>
            <div class="metadata">
                <div class="metadata-item">
                    <div class="metadata-label">Organization</div>
                    <div class="metadata-value">$(ConvertTo-SafeHtml -Text $OrganizationName)</div>
                </div>
                <div class="metadata-item">
                    <div class="metadata-label">Audit Date</div>
                    <div class="metadata-value">$AuditDate</div>
                </div>
                <div class="metadata-item">
                    <div class="metadata-label">Audit ID</div>
                    <div class="metadata-value">$AuditID</div>
                </div>
                <div class="metadata-item">
                    <div class="metadata-label">Script Version</div>
                    <div class="metadata-value">$ScriptVersion</div>
                </div>
                <div class="metadata-item">
                    <div class="metadata-label">Total Mailboxes</div>
                    <div class="metadata-value">$TotalMailboxes</div>
                </div>
                <div class="metadata-item">
                    <div class="metadata-label">Audit Scope</div>
                    <div class="metadata-value">$(ConvertTo-SafeHtml -Text $DomainScope)</div>
                </div>
            </div>
        </div>
"@
}

function Generate-ComplianceDashboard {
    param(
        [int]$TotalControls,
        [int]$CompliantControls,
        [int]$ReviewRequiredControls,
        [int]$NonCompliantControls,
        [int]$ErrorControls,
        [double]$OverallComplianceScore,
        [int]$CriticalRiskControls,
        [int]$HighRiskControls,
        [int]$MediumRiskControls,
        [int]$LowRiskControls
    )

    $CompliancePercentage = if ($TotalControls -gt 0) { [math]::Round(($CompliantControls / $TotalControls) * 100, 1) } else { 0 }

    return @"
        <div class="section">
            <div class="section-header">
                <h2>Executive Dashboard</h2>
                <div class="description">Overall compliance status and key security metrics</div>
            </div>
            <div class="section-content">
                <div class="dashboard">
                    <div class="dashboard-card">
                        <h3>Overall Compliance Score</h3>
                        <div class="dashboard-value" style="color: $(if ($OverallComplianceScore -ge 80) { '#28a745' } elseif ($OverallComplianceScore -ge 60) { '#ffc107' } else { '#dc3545' })">
                            $(Format-Number -Number $OverallComplianceScore -DecimalPlaces 1)%
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: $OverallComplianceScore%"></div>
                        </div>
                        <div class="dashboard-label">Average compliance across all controls</div>
                    </div>

                    <div class="dashboard-card">
                        <h3>Controls Assessment</h3>
                        <div class="dashboard-value" style="color: #667eea">$TotalControls</div>
                        <div class="dashboard-label">
                            <span style="color: #28a745">✓ $CompliantControls Compliant</span><br>
                            <span style="color: #fd7e14">⚠ $ReviewRequiredControls Review Required</span><br>
                            <span style="color: #dc3545">✗ $NonCompliantControls Non-Compliant</span>
                            $(if ($ErrorControls -gt 0) { "<br><span style='color: #6c757d'>⚡ $ErrorControls Errors</span>" } else { "" })
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <h3>Risk Distribution</h3>
                        <div class="dashboard-value" style="color: $(if ($CriticalRiskControls -gt 0 -or $HighRiskControls -gt 0) { '#dc3545' } elseif ($MediumRiskControls -gt 0) { '#ffc107' } else { '#28a745' })">
                            $(if ($CriticalRiskControls -gt 0) { 'CRITICAL' } elseif ($HighRiskControls -gt 0) { 'HIGH' } elseif ($MediumRiskControls -gt 0) { 'MEDIUM' } else { 'LOW' })
                        </div>
                        <div class="dashboard-label">
                            <span style="color: #dc3545">🔴 $CriticalRiskControls Critical</span><br>
                            <span style="color: #fd7e14">🟠 $HighRiskControls High</span><br>
                            <span style="color: #ffc107">🟡 $MediumRiskControls Medium</span><br>
                            <span style="color: #28a745">🟢 $LowRiskControls Low</span>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <h3>Compliance Rate</h3>
                        <div class="dashboard-value" style="color: $(if ($CompliancePercentage -ge 80) { '#28a745' } elseif ($CompliancePercentage -ge 60) { '#ffc107' } else { '#dc3545' })">
                            $CompliancePercentage%
                        </div>
                        <div class="progress-bar">
                            <div class="progress-fill" style="width: $CompliancePercentage%"></div>
                        </div>
                        <div class="dashboard-label">Controls meeting compliance requirements</div>
                    </div>
                </div>

                $(if ($CriticalRiskControls -gt 0 -or $HighRiskControls -gt 0) {
                    '<div class="alert alert-danger">
                        <strong>⚠️ Immediate Action Required:</strong> Critical or high-risk security controls have been identified that require immediate attention to maintain security posture.
                    </div>'
                } elseif ($ReviewRequiredControls -gt 0) {
                    '<div class="alert alert-warning">
                        <strong>📋 Review Recommended:</strong> Some controls require review to ensure optimal security configuration and compliance.
                    </div>'
                } else {
                    '<div class="alert alert-info">
                        <strong>✅ Good Security Posture:</strong> All assessed controls are within acceptable compliance thresholds.
                    </div>'
                })
            </div>
        </div>
"@
}

function Generate-ControlSummaryTable {
    param([array]$ControlSummaryData)

    if (-not $ControlSummaryData -or $ControlSummaryData.Count -eq 0) {
        return '<div class="alert alert-warning">No control data available for display.</div>'
    }

    $TableRows = ""
    foreach ($Control in $ControlSummaryData) {
        $RiskColor = Get-RiskLevelColor -RiskLevel $Control.RiskLevel
        $ComplianceColor = Get-ComplianceStatusColor -Status $Control.ComplianceStatus
        $ComplianceScore = Format-Number -Number $Control.ComplianceScore -DecimalPlaces 0
        $CVSSScore = Format-Number -Number $Control.CVSSScore -DecimalPlaces 1

        $StatusIcon = switch ($Control.ComplianceStatus.ToLower()) {
            "compliant" { "✅" }
            "review required" { "⚠️" }
            "non-compliant" { "❌" }
            default { "❓" }
        }

        $ErrorIndicator = if ($Control.HasError) {
            "<br /><small style='color: #dc3545;'>⚡ Error: $(ConvertTo-SafeHtml -Text $Control.ErrorMessage)</small>"
        } else { "" }

        $TableRows += @"
            <tr>
                <td><strong>$(ConvertTo-SafeHtml -Text $Control.ControlID)</strong><br />
                    <small style="color: #6c757d;">$(ConvertTo-SafeHtml -Text $Control.Category)</small></td>
                <td>$(ConvertTo-SafeHtml -Text $Control.ControlName)$ErrorIndicator</td>
                <td><span class="status-badge risk-$($Control.RiskLevel.ToLower())" style="background-color: $RiskColor;">$($Control.RiskLevel)</span></td>
                <td>$StatusIcon <span class="status-badge compliance-$($Control.ComplianceStatus.ToLower().Replace(' ', '-'))" style="background-color: $ComplianceColor;">$($Control.ComplianceStatus)</span></td>
                <td style="text-align: center;"><strong>$ComplianceScore%</strong></td>
                <td style="text-align: center;">$CVSSScore</td>
                <td style="max-width: 300px; word-wrap: break-word;">$(ConvertTo-SafeHtml -Text $Control.Finding)</td>
            </tr>
"@
    }

    return @"
        <div class="section">
            <div class="section-header">
                <h2>Security Control Analysis</h2>
                <div class="description">Detailed assessment of each mailbox security control</div>
            </div>
            <div class="section-content">
                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>Control ID</th>
                                <th>Control Name</th>
                                <th>Risk Level</th>
                                <th>Compliance Status</th>
                                <th>Score</th>
                                <th>CVSS</th>
                                <th>Finding</th>
                            </tr>
                        </thead>
                        <tbody>
                            $TableRows
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
"@
}

function Generate-PermissionAnalysisSection {
    param([hashtable]$AuditData)

    $PermissionSections = @()

    # Full Access Permissions
    if ($AuditData.ContainsKey("MBX_2_1_FullAccessPermissions")) {
        $FullAccessData = $AuditData["MBX_2_1_FullAccessPermissions"]
        if ($FullAccessData.FullAccessPermissions -and $FullAccessData.FullAccessPermissions.Count -gt 0) {
            $PermissionRows = ""
            foreach ($Permission in $FullAccessData.FullAccessPermissions) {
                $RiskLevel = if ($Permission.IsInherited -eq $false) { "High" } else { "Medium" }
                $RiskColor = Get-RiskLevelColor -RiskLevel $RiskLevel

                $PermissionRows += "<tr>" +
                    "<td>$(ConvertTo-SafeHtml -Text $Permission.MailboxDisplayName)</td>" +
                    "<td><small>$(ConvertTo-SafeHtml -Text $Permission.MailboxPrimarySmtpAddress)</small></td>" +
                    "<td>$(ConvertTo-SafeHtml -Text $Permission.User)</td>" +
                    "<td>$(ConvertTo-SafeHtml -Text $Permission.AccessRights)</td>" +
                    "<td>$(if ($Permission.IsInherited) { 'Yes' } else { 'No' })</td>" +
                    "<td><span class='status-badge risk-$($RiskLevel.ToLower())' style='background-color: $RiskColor;'>$RiskLevel</span></td>" +
                    "</tr>`n"
            }

            $PermissionSections += "<div class='section'>" +
                "<div class='section-header'>" +
                "<h2>Full Access Permissions Analysis</h2>" +
                "<div class='description'>Mailbox delegations with full access rights - Total: $($FullAccessData.TotalFullAccessPermissions)</div>" +
                "</div>" +
                "<div class='section-content'>" +
                "<div class='alert alert-info'>" +
                "<strong>Assessment Summary:</strong> $(ConvertTo-SafeHtml -Text $FullAccessData.Finding)<br />" +
                "<strong>Sample Size:</strong> $($FullAccessData.SampleSize) mailboxes analyzed<br />" +
                "<strong>Unique Users with Access:</strong> $($FullAccessData.UniqueUsersWithFullAccess)" +
                "</div>" +
                "<div class='table-responsive'>" +
                "<table>" +
                "<thead>" +
                "<tr>" +
                "<th>Mailbox Name</th>" +
                "<th>Email Address</th>" +
                "<th>Delegated User</th>" +
                "<th>Access Rights</th>" +
                "<th>Inherited</th>" +
                "<th>Risk Level</th>" +
                "</tr>" +
                "</thead>" +
                "<tbody>" +
                "$PermissionRows" +
                "</tbody>" +
                "</table>" +
                "</div>" +
                "<div class='recommendations'>" +
                "<h4>Recommendations</h4>" +
                "<p>$(ConvertTo-SafeHtml -Text $FullAccessData.Recommendation)</p>" +
                "</div>" +
                "</div>" +
                "</div>`n"
        }
    }

    # Send-As Permissions
    if ($AuditData.ContainsKey("MBX_4_1_SendAsPermissions")) {
        $SendAsData = $AuditData["MBX_4_1_SendAsPermissions"]
        if ($SendAsData.SendAsPermissions -and $SendAsData.SendAsPermissions.Count -gt 0) {
            $SendAsRows = ""
            foreach ($Permission in $SendAsData.SendAsPermissions) {
                $RiskLevel = if ($Permission.IsInherited -eq $false) { "Medium" } else { "Low" }
                $RiskColor = Get-RiskLevelColor -RiskLevel $RiskLevel

                $SendAsRows += @"
                    <tr>
                        <td>$(ConvertTo-SafeHtml -Text $Permission.MailboxDisplayName)</td>
                        <td><small>$(ConvertTo-SafeHtml -Text $Permission.MailboxPrimarySmtpAddress)</small></td>
                        <td>$(ConvertTo-SafeHtml -Text $Permission.User)</td>
                        <td>$(ConvertTo-SafeHtml -Text $Permission.ExtendedRights)</td>
                        <td>$(ConvertTo-SafeHtml -Text $Permission.AccessControlType)</td>
                        <td><span class="status-badge risk-$($RiskLevel.ToLower())" style="background-color: $RiskColor;">$RiskLevel</span></td>
                    </tr>
"@
            }

            $PermissionSections += @"
                <div class="section">
                    <div class="section-header">
                        <h2>Send-As Permissions Analysis</h2>
                        <div class="description">Email sending delegations - Total: $($SendAsData.TotalSendAsPermissions)</div>
                    </div>
                    <div class="section-content">
                        <div class="alert alert-info">
                            <strong>Assessment Summary:</strong> $(ConvertTo-SafeHtml -Text $SendAsData.Finding)<br>
                            <strong>Sample Size:</strong> $($SendAsData.SampleSize) mailboxes analyzed<br>
                            <strong>Unique Users with Send-As:</strong> $($SendAsData.UniqueUsersWithSendAs)
                        </div>
                        <div class="table-responsive">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Mailbox Name</th>
                                        <th>Email Address</th>
                                        <th>Delegated User</th>
                                        <th>Extended Rights</th>
                                        <th>Access Type</th>
                                        <th>Risk Level</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    $SendAsRows
                                </tbody>
                            </table>
                        </div>
                        <div class="recommendations">
                            <h4>Recommendations</h4>
                            <p>$(ConvertTo-SafeHtml -Text $SendAsData.Recommendation)</p>
                        </div>
                    </div>
                </div>
"@
        }
    }

    # Send-On-Behalf Permissions
    if ($AuditData.ContainsKey("MBX_5_1_SendOnBehalfPermissions")) {
        $SendOnBehalfData = $AuditData["MBX_5_1_SendOnBehalfPermissions"]
        if ($SendOnBehalfData.SendOnBehalfPermissions -and $SendOnBehalfData.SendOnBehalfPermissions.Count -gt 0) {
            $SendOnBehalfRows = ""
            foreach ($Permission in $SendOnBehalfData.SendOnBehalfPermissions) {
                $SendOnBehalfRows += @"
                    <tr>
                        <td>$(ConvertTo-SafeHtml -Text $Permission.MailboxDisplayName)</td>
                        <td><small>$(ConvertTo-SafeHtml -Text $Permission.MailboxPrimarySmtpAddress)</small></td>
                        <td>$(ConvertTo-SafeHtml -Text $Permission.DelegateUser)</td>
                        <td>$(ConvertTo-SafeHtml -Text $Permission.PermissionType)</td>
                        <td><span class="status-badge risk-low" style="background-color: #28a745;">Low</span></td>
                    </tr>
"@
            }

            $PermissionSections += @"
                <div class="section">
                    <div class="section-header">
                        <h2>Send-On-Behalf Permissions Analysis</h2>
                        <div class="description">Send-on-behalf delegations - Total: $($SendOnBehalfData.TotalSendOnBehalfPermissions)</div>
                    </div>
                    <div class="section-content">
                        <div class="alert alert-info">
                            <strong>Assessment Summary:</strong> $(ConvertTo-SafeHtml -Text $SendOnBehalfData.Finding)<br>
                            <strong>Sample Size:</strong> $($SendOnBehalfData.SampleSize) mailboxes analyzed<br>
                            <strong>Unique Users with Send-On-Behalf:</strong> $($SendOnBehalfData.UniqueUsersWithSendOnBehalf)
                        </div>
                        <div class="table-responsive">
                            <table>
                                <thead>
                                    <tr>
                                        <th>Mailbox Name</th>
                                        <th>Email Address</th>
                                        <th>Delegate User</th>
                                        <th>Permission Type</th>
                                        <th>Risk Level</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    $SendOnBehalfRows
                                </tbody>
                            </table>
                        </div>
                        <div class="recommendations">
                            <h4>Recommendations</h4>
                            <p>$(ConvertTo-SafeHtml -Text $SendOnBehalfData.Recommendation)</p>
                        </div>
                    </div>
                </div>
"@
        }
    }

    return $PermissionSections -join "`n"
}

function Generate-CrossDomainAnalysisSection {
    param([hashtable]$AuditData)

    if (-not $AuditData.ContainsKey("MBX_2_1_FullAccessPermissions")) {
        return ""
    }

    $FullAccessData = $AuditData["MBX_2_1_FullAccessPermissions"]
    if (-not $FullAccessData.ContainsKey("CrossDomainAnalysis")) {
        return ""
    }

    $CrossDomainData = $FullAccessData.CrossDomainAnalysis
    if ($CrossDomainData.ContainsKey("Error") -or $CrossDomainData.Status -eq "Skipped") {
        return @"
            <div class="section">
                <div class="section-header">
                    <h2>Cross-Domain Risk Assessment</h2>
                    <div class="description">Analysis of cross-domain permission relationships</div>
                </div>
                <div class="section-content">
                    <div class="alert alert-warning">
                        <strong>Analysis Unavailable:</strong> Cross-domain analysis was skipped or encountered an error.
                        $(if ($CrossDomainData.ContainsKey("Error")) { "Error: $(ConvertTo-SafeHtml -Text $CrossDomainData.Error)" } else { "Reason: $(ConvertTo-SafeHtml -Text $CrossDomainData.Reason)" })
                    </div>
                </div>
            </div>
"@
    }

    $RiskAssessment = $CrossDomainData.RiskAssessment
    if (-not $RiskAssessment) {
        return ""
    }

    $CrossDomainCount = if ($RiskAssessment.CrossDomainRelationships) { $RiskAssessment.CrossDomainRelationships } else { 0 }
    $TotalRelationships = if ($RiskAssessment.TotalPermissionRelationships) { $RiskAssessment.TotalPermissionRelationships } else { 0 }
    $HighRiskCount = if ($RiskAssessment.HighRiskRelationships) { $RiskAssessment.HighRiskRelationships } else { 0 }
    $CrossDomainPercentage = if ($RiskAssessment.CrossDomainPercentage) { $RiskAssessment.CrossDomainPercentage } else { 0 }

    $RiskDistribution = $RiskAssessment.RiskDistribution
    $HighRisk = if ($RiskDistribution -and $RiskDistribution.High) { $RiskDistribution.High } else { 0 }
    $MediumRisk = if ($RiskDistribution -and $RiskDistribution.Medium) { $RiskDistribution.Medium } else { 0 }
    $LowRisk = if ($RiskDistribution -and $RiskDistribution.Low) { $RiskDistribution.Low } else { 0 }

    return @"
        <div class="section">
            <div class="section-header">
                <h2>Cross-Domain Risk Assessment</h2>
                <div class="description">Analysis of cross-domain permission relationships and security implications</div>
            </div>
            <div class="section-content">
                <div class="dashboard">
                    <div class="dashboard-card">
                        <h3>Cross-Domain Relationships</h3>
                        <div class="dashboard-value" style="color: $(if ($CrossDomainCount -gt 0) { '#fd7e14' } else { '#28a745' })">$CrossDomainCount</div>
                        <div class="dashboard-label">Out of $TotalRelationships total relationships ($CrossDomainPercentage%)</div>
                    </div>

                    <div class="dashboard-card">
                        <h3>High Risk Relationships</h3>
                        <div class="dashboard-value" style="color: $(if ($HighRiskCount -gt 0) { '#dc3545' } else { '#28a745' })">$HighRiskCount</div>
                        <div class="dashboard-label">Requiring immediate attention</div>
                    </div>

                    <div class="dashboard-card">
                        <h3>Risk Distribution</h3>
                        <div class="dashboard-value" style="color: $(if ($HighRisk -gt 0) { '#dc3545' } elseif ($MediumRisk -gt 0) { '#ffc107' } else { '#28a745' })">
                            $(if ($HighRisk -gt 0) { 'HIGH' } elseif ($MediumRisk -gt 0) { 'MEDIUM' } else { 'LOW' })
                        </div>
                        <div class="dashboard-label">
                            🔴 $HighRisk High | 🟡 $MediumRisk Medium | 🟢 $LowRisk Low
                        </div>
                    </div>
                </div>

                $(if ($CrossDomainCount -gt 0) {
                    '<div class="alert alert-warning">
                        <strong>⚠️ Cross-Domain Relationships Detected:</strong> ' + $CrossDomainCount + ' cross-domain permission relationships have been identified. These require careful review to ensure they align with business requirements and security policies.
                    </div>'
                } else {
                    '<div class="alert alert-info">
                        <strong>✅ No Cross-Domain Risks:</strong> All permission relationships are within the same domain, reducing cross-domain security risks.
                    </div>'
                })
            </div>
        </div>
"@
}

function Generate-AdministratorDiscoverySection {
    param([hashtable]$AuditData)

    if (-not $AuditData.ContainsKey("AdministratorDiscovery")) {
        return ""
    }

    $AdminData = $AuditData["AdministratorDiscovery"]
    if ($AdminData.ContainsKey("Error")) {
        return @"
            <div class="section">
                <div class="section-header">
                    <h2>Administrator Discovery Report</h2>
                    <div class="description">Exchange administrator role assignments and domain mapping</div>
                </div>
                <div class="section-content">
                    <div class="alert alert-warning">
                        <strong>Discovery Failed:</strong> Administrator discovery encountered an error: $(ConvertTo-SafeHtml -Text $AdminData.Error)
                    </div>
                </div>
            </div>
"@
    }

    $RoleAssignments = $AdminData.RoleAssignments
    if (-not $RoleAssignments -or $RoleAssignments.Count -eq 0) {
        return @"
            <div class="section">
                <div class="section-header">
                    <h2>Administrator Discovery Report</h2>
                    <div class="description">Exchange administrator role assignments and domain mapping</div>
                </div>
                <div class="section-content">
                    <div class="alert alert-info">
                        <strong>No Administrator Data:</strong> No Exchange administrator role assignments were discovered.
                    </div>
                </div>
            </div>
"@
    }

    # Generate administrator table
    $AdminRows = ""
    foreach ($Assignment in $RoleAssignments) {
        $ScopeType = if ($Assignment.Scope -and $Assignment.Scope.Type) { $Assignment.Scope.Type } else { "Unknown" }
        $IsOrgWide = if ($Assignment.Scope -and $Assignment.Scope.IsOrganizationWide) { "Yes" } else { "No" }
        $AdminClassColor = switch ($Assignment.AdminClassification) {
            "Exchange-Administrator" { "#dc3545" }
            "Mailbox-Administrator" { "#fd7e14" }
            "Read-Only-Administrator" { "#28a745" }
            default { "#6c757d" }
        }

        $AdminRows += @"
            <tr>
                <td>$(ConvertTo-SafeHtml -Text $Assignment.RoleAssignee)</td>
                <td>$(ConvertTo-SafeHtml -Text $Assignment.AssigneeDomain)</td>
                <td>$(ConvertTo-SafeHtml -Text $Assignment.Role)</td>
                <td><span class="status-badge" style="background-color: $AdminClassColor; color: white;">$($Assignment.AdminClassification)</span></td>
                <td>$(ConvertTo-SafeHtml -Text $ScopeType)</td>
                <td>$IsOrgWide</td>
                <td>$(if ($Assignment.IsEnabled) { "✅" } else { "❌" })</td>
            </tr>
"@
    }

    # Calculate summary statistics
    $TotalAdmins = $RoleAssignments.Count
    $ExchangeAdmins = ($RoleAssignments | Where-Object { $_.AdminClassification -eq "Exchange-Administrator" }).Count
    $MailboxAdmins = ($RoleAssignments | Where-Object { $_.AdminClassification -eq "Mailbox-Administrator" }).Count
    $ReadOnlyAdmins = ($RoleAssignments | Where-Object { $_.AdminClassification -eq "Read-Only-Administrator" }).Count
    $OrgWideAdmins = ($RoleAssignments | Where-Object { $_.Scope -and $_.Scope.IsOrganizationWide }).Count

    # Domain distribution
    $DomainMap = $AdminData.AdminDomainMap
    $DomainSummary = ""
    if ($DomainMap) {
        foreach ($Domain in $DomainMap.Keys) {
            $DomainCount = if ($DomainMap[$Domain]) { $DomainMap[$Domain].Count } else { 0 }
            $DomainSummary += "<li><strong>$(ConvertTo-SafeHtml -Text $Domain):</strong> $DomainCount administrators</li>"
        }
    }

    return @"
        <div class="section">
            <div class="section-header">
                <h2>Administrator Discovery Report</h2>
                <div class="description">Exchange administrator role assignments and domain mapping - Total: $TotalAdmins administrators</div>
            </div>
            <div class="section-content">
                <div class="dashboard">
                    <div class="dashboard-card">
                        <h3>Administrator Types</h3>
                        <div class="dashboard-value" style="color: #667eea">$TotalAdmins</div>
                        <div class="dashboard-label">
                            <span style="color: #dc3545">🔴 $ExchangeAdmins Exchange Admins</span><br>
                            <span style="color: #fd7e14">🟠 $MailboxAdmins Mailbox Admins</span><br>
                            <span style="color: #28a745">🟢 $ReadOnlyAdmins Read-Only Admins</span>
                        </div>
                    </div>

                    <div class="dashboard-card">
                        <h3>Organization-Wide Access</h3>
                        <div class="dashboard-value" style="color: $(if ($OrgWideAdmins -gt 0) { '#fd7e14' } else { '#28a745' })">$OrgWideAdmins</div>
                        <div class="dashboard-label">Administrators with organization-wide scope</div>
                    </div>

                    <div class="dashboard-card">
                        <h3>Domain Distribution</h3>
                        <div class="dashboard-value" style="color: #667eea">$($DomainMap.Keys.Count)</div>
                        <div class="dashboard-label">Unique domains with administrators</div>
                    </div>
                </div>

                $(if ($DomainSummary) {
                    "<div class='alert alert-info'>
                        <strong>Domain Distribution:</strong>
                        <ul style='margin-top: 10px; margin-bottom: 0;'>$DomainSummary</ul>
                    </div>"
                })

                <div class="table-responsive">
                    <table>
                        <thead>
                            <tr>
                                <th>Administrator</th>
                                <th>Domain</th>
                                <th>Role</th>
                                <th>Classification</th>
                                <th>Scope Type</th>
                                <th>Org-Wide</th>
                                <th>Enabled</th>
                            </tr>
                        </thead>
                        <tbody>
                            $AdminRows
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
"@
}

function Generate-RecommendationsSection {
    param([array]$ControlSummaryData)

    $Recommendations = @()

    # Prioritize recommendations based on risk level and compliance status
    $HighPriorityControls = $ControlSummaryData | Where-Object {
        $_.RiskLevel -in @("Critical", "High") -and $_.ComplianceStatus -ne "Compliant"
    } | Sort-Object @{Expression={if($_.RiskLevel -eq "Critical"){1}else{2}}}, ComplianceScore

    $MediumPriorityControls = $ControlSummaryData | Where-Object {
        $_.RiskLevel -eq "Medium" -and $_.ComplianceStatus -ne "Compliant"
    } | Sort-Object ComplianceScore

    $LowPriorityControls = $ControlSummaryData | Where-Object {
        $_.RiskLevel -eq "Low" -and $_.ComplianceStatus -ne "Compliant"
    } | Sort-Object ComplianceScore

    # High Priority Recommendations
    if ($HighPriorityControls.Count -gt 0) {
        $HighPriorityList = ""
        foreach ($Control in $HighPriorityControls) {
            $HighPriorityList += "<li><strong>$($Control.ControlID) - $($Control.ControlName):</strong> $(ConvertTo-SafeHtml -Text $Control.Recommendation)</li>"
        }

        $Recommendations += @"
            <div class="alert alert-danger">
                <h4 style="margin-bottom: 15px;">🚨 High Priority Actions (Immediate Attention Required)</h4>
                <ul style="margin-bottom: 0;">$HighPriorityList</ul>
            </div>
"@
    }

    # Medium Priority Recommendations
    if ($MediumPriorityControls.Count -gt 0) {
        $MediumPriorityList = ""
        foreach ($Control in $MediumPriorityControls) {
            $MediumPriorityList += "<li><strong>$($Control.ControlID) - $($Control.ControlName):</strong> $(ConvertTo-SafeHtml -Text $Control.Recommendation)</li>"
        }

        $Recommendations += @"
            <div class="alert alert-warning">
                <h4 style="margin-bottom: 15px;">⚠️ Medium Priority Actions (Review Within 30 Days)</h4>
                <ul style="margin-bottom: 0;">$MediumPriorityList</ul>
            </div>
"@
    }

    # Low Priority Recommendations
    if ($LowPriorityControls.Count -gt 0) {
        $LowPriorityList = ""
        foreach ($Control in $LowPriorityControls) {
            $LowPriorityList += "<li><strong>$($Control.ControlID) - $($Control.ControlName):</strong> $(ConvertTo-SafeHtml -Text $Control.Recommendation)</li>"
        }

        $Recommendations += @"
            <div class="alert alert-info">
                <h4 style="margin-bottom: 15px;">ℹ️ Low Priority Actions (Review Within 90 Days)</h4>
                <ul style="margin-bottom: 0;">$LowPriorityList</ul>
            </div>
"@
    }

    # General best practices
    $GeneralRecommendations = @"
        <div class="recommendations">
            <h4>General Security Best Practices</h4>
            <ul>
                <li>Implement regular access reviews for all mailbox permissions (quarterly recommended)</li>
                <li>Enable comprehensive audit logging for all Exchange activities</li>
                <li>Use principle of least privilege for all mailbox delegations</li>
                <li>Document business justification for all cross-domain permissions</li>
                <li>Implement automated monitoring for new permission assignments</li>
                <li>Regular training for administrators on Exchange security best practices</li>
                <li>Establish approval workflows for high-privilege role assignments</li>
            </ul>
        </div>
"@

    if ($Recommendations.Count -eq 0) {
        $Recommendations += @"
            <div class="alert alert-info">
                <h4>✅ Excellent Security Posture</h4>
                <p>All assessed controls are compliant. Continue monitoring and maintain current security practices.</p>
            </div>
"@
    }

    return @"
        <div class="section">
            <div class="section-header">
                <h2>Actionable Recommendations</h2>
                <div class="description">Prioritized remediation actions based on risk assessment</div>
            </div>
            <div class="section-content">
                $($Recommendations -join "`n")
                $GeneralRecommendations
            </div>
        </div>
"@
}

# ================================================================================
# CSV EXPORT FUNCTIONS
# ================================================================================

function Export-ControlSummaryToCsv {
    param(
        [array]$ControlSummaryData,
        [string]$OutputDirectory
    )

    if (-not $ControlSummaryData -or $ControlSummaryData.Count -eq 0) {
        Write-Host "  No control summary data to export" -ForegroundColor Yellow
        return
    }

    try {
        $CsvPath = Join-Path $OutputDirectory "Control-Summary-Export.csv"
        $ControlSummaryData | Select-Object ControlID, ControlName, Category, RiskLevel, ComplianceStatus, ComplianceScore, CVSSScore, Finding, Recommendation, HasError, ErrorMessage |
            Export-Csv -Path $CsvPath -NoTypeInformation -Encoding UTF8

        Write-Host "  Control summary exported: $CsvPath" -ForegroundColor Green
    }
    catch {
        Write-Host "  [ERROR] Failed to export control summary: $($_.Exception.Message)" -ForegroundColor Red
    }
}

function Export-PermissionsToCsv {
    param(
        [hashtable]$AuditData,
        [string]$OutputDirectory
    )

    # Export Full Access Permissions
    if ($AuditData.ContainsKey("MBX_2_1_FullAccessPermissions")) {
        $FullAccessData = $AuditData["MBX_2_1_FullAccessPermissions"]
        if ($FullAccessData.FullAccessPermissions -and $FullAccessData.FullAccessPermissions.Count -gt 0) {
            try {
                $CsvPath = Join-Path $OutputDirectory "Full-Access-Permissions-Export.csv"
                $FullAccessData.FullAccessPermissions | Export-Csv -Path $CsvPath -NoTypeInformation -Encoding UTF8
                Write-Host "  Full Access permissions exported: $CsvPath" -ForegroundColor Green
            }
            catch {
                Write-Host "  [ERROR] Failed to export Full Access permissions: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }

    # Export Send-As Permissions
    if ($AuditData.ContainsKey("MBX_4_1_SendAsPermissions")) {
        $SendAsData = $AuditData["MBX_4_1_SendAsPermissions"]
        if ($SendAsData.SendAsPermissions -and $SendAsData.SendAsPermissions.Count -gt 0) {
            try {
                $CsvPath = Join-Path $OutputDirectory "Send-As-Permissions-Export.csv"
                $SendAsData.SendAsPermissions | Export-Csv -Path $CsvPath -NoTypeInformation -Encoding UTF8
                Write-Host "  Send-As permissions exported: $CsvPath" -ForegroundColor Green
            }
            catch {
                Write-Host "  [ERROR] Failed to export Send-As permissions: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }

    # Export Send-On-Behalf Permissions
    if ($AuditData.ContainsKey("MBX_5_1_SendOnBehalfPermissions")) {
        $SendOnBehalfData = $AuditData["MBX_5_1_SendOnBehalfPermissions"]
        if ($SendOnBehalfData.SendOnBehalfPermissions -and $SendOnBehalfData.SendOnBehalfPermissions.Count -gt 0) {
            try {
                $CsvPath = Join-Path $OutputDirectory "Send-On-Behalf-Permissions-Export.csv"
                $SendOnBehalfData.SendOnBehalfPermissions | Export-Csv -Path $CsvPath -NoTypeInformation -Encoding UTF8
                Write-Host "  Send-On-Behalf permissions exported: $CsvPath" -ForegroundColor Green
            }
            catch {
                Write-Host "  [ERROR] Failed to export Send-On-Behalf permissions: $($_.Exception.Message)" -ForegroundColor Red
            }
        }
    }
}

function Export-AdministratorsToCsv {
    param(
        [hashtable]$AuditData,
        [string]$OutputDirectory
    )

    if (-not $AuditData.ContainsKey("AdministratorDiscovery")) {
        return
    }

    $AdminData = $AuditData["AdministratorDiscovery"]
    if ($AdminData.ContainsKey("Error") -or -not $AdminData.RoleAssignments -or $AdminData.RoleAssignments.Count -eq 0) {
        return
    }

    try {
        $CsvPath = Join-Path $OutputDirectory "Administrator-Discovery-Export.csv"
        $AdminData.RoleAssignments | Select-Object RoleAssignee, AssigneeDomain, AssigneeType, Role, RoleType, AdminClassification, AssignmentMethod, IsEnabled |
            Export-Csv -Path $CsvPath -NoTypeInformation -Encoding UTF8

        Write-Host "  Administrator discovery exported: $CsvPath" -ForegroundColor Green
    }
    catch {
        Write-Host "  [ERROR] Failed to export administrator discovery: $($_.Exception.Message)" -ForegroundColor Red
    }
}

# ================================================================================
# MAIN REPORT GENERATION
# ================================================================================

Write-Host "Step 5/6: Generating HTML report..." -ForegroundColor Yellow

try {
    # Generate HTML sections
    $HtmlHeader = Generate-HtmlHeader -AuditMetadata $AuditMetadata -ComplianceSummary $ComplianceSummary
    $ComplianceDashboard = Generate-ComplianceDashboard -TotalControls $TotalControls -CompliantControls $CompliantControls -ReviewRequiredControls $ReviewRequiredControls -NonCompliantControls $NonCompliantControls -ErrorControls $ErrorControls -OverallComplianceScore $OverallComplianceScore -CriticalRiskControls $CriticalRiskControls -HighRiskControls $HighRiskControls -MediumRiskControls $MediumRiskControls -LowRiskControls $LowRiskControls
    $ControlSummaryTable = Generate-ControlSummaryTable -ControlSummaryData $ControlSummaryData
    $PermissionAnalysis = Generate-PermissionAnalysisSection -AuditData $AuditDataHash
    $CrossDomainAnalysis = Generate-CrossDomainAnalysisSection -AuditData $AuditDataHash
    $AdministratorDiscovery = Generate-AdministratorDiscoverySection -AuditData $AuditDataHash
    $Recommendations = Generate-RecommendationsSection -ControlSummaryData $ControlSummaryData

    # Generate footer
    $ReportEndTime = Get-Date
    $ReportDuration = $ReportEndTime - $ReportStartTime
    $Footer = @"
        <div class="footer">
            <p><strong>Exchange Mailbox Security Audit Report</strong></p>
            <p>Generated by E.Z. Consultancy Exchange Audit Report Generator v1.0.0</p>
            <p>Report Generated: $($ReportEndTime.ToString('yyyy-MM-dd HH:mm:ss')) | Duration: $([math]::Round($ReportDuration.TotalSeconds, 2)) seconds</p>
            <p>Report ID: $ReportID | Source Audit ID: $($AuditMetadata.AuditID)</p>
            <p style="margin-top: 15px; font-size: 0.8em; color: #999;">
                This report contains sensitive security information. Handle according to your organization's data classification policies.
            </p>
        </div>
"@

    # Combine all sections into complete HTML
    $CompleteHtml = @"
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Exchange Mailbox Security Audit Report - $($AuditMetadata.OrganizationName)</title>
    $CssStyles
</head>
<body>
    <div class="container">
        $HtmlHeader
        $ComplianceDashboard
        $ControlSummaryTable
        $PermissionAnalysis
        $CrossDomainAnalysis
        $AdministratorDiscovery
        $Recommendations
        $Footer
    </div>

    <script>
        // Add some basic interactivity
        document.addEventListener('DOMContentLoaded', function() {
            // Add click-to-copy functionality for audit IDs
            const auditIds = document.querySelectorAll('.metadata-value');
            auditIds.forEach(function(element) {
                if (element.textContent.match(/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/i)) {
                    element.style.cursor = 'pointer';
                    element.title = 'Click to copy';
                    element.addEventListener('click', function() {
                        navigator.clipboard.writeText(element.textContent).then(function() {
                            const originalText = element.textContent;
                            element.textContent = 'Copied!';
                            setTimeout(function() {
                                element.textContent = originalText;
                            }, 1000);
                        });
                    });
                }
            });

            // Add print functionality
            if (window.location.search.includes('print=true')) {
                window.print();
            }
        });
    </script>
</body>
</html>
"@

    # Save HTML report
    $CompleteHtml | Out-File -FilePath $OutputPath -Encoding UTF8 -ErrorAction Stop
    Write-Host "  HTML report generated successfully: $OutputPath" -ForegroundColor Green

    # Console output if requested
    if ($ConsoleOutput) {
        Write-Host ""
        Write-Host "=== CONSOLE SUMMARY ===" -ForegroundColor Yellow
        Write-Host "Organization: $($AuditMetadata.OrganizationName)" -ForegroundColor White
        Write-Host "Overall Compliance Score: $OverallComplianceScore%" -ForegroundColor $(if ($OverallComplianceScore -ge 80) { "Green" } elseif ($OverallComplianceScore -ge 60) { "Yellow" } else { "Red" })
        Write-Host "Total Controls: $TotalControls | Compliant: $CompliantControls | Review Required: $ReviewRequiredControls" -ForegroundColor White
        Write-Host "Risk Distribution: Critical: $CriticalRiskControls | High: $HighRiskControls | Medium: $MediumRiskControls | Low: $LowRiskControls" -ForegroundColor White
        Write-Host "========================" -ForegroundColor Yellow
    }

}
catch {
    Write-Host "[ERROR] Failed to generate HTML report: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# ================================================================================
# CSV EXPORT PROCESSING
# ================================================================================

if ($ExportCsv) {
    Write-Host "Step 6/6: Exporting CSV files for detailed analysis..." -ForegroundColor Yellow

    try {
        # Create CSV export directory
        $CsvDirectory = [System.IO.Path]::GetDirectoryName($OutputPath)
        $CsvFolderName = [System.IO.Path]::GetFileNameWithoutExtension($OutputPath) + "-CSV-Exports"
        $CsvExportPath = Join-Path $CsvDirectory $CsvFolderName

        if (-not (Test-Path -Path $CsvExportPath)) {
            New-Item -Path $CsvExportPath -ItemType Directory -Force | Out-Null
        }

        Write-Host "  CSV export directory: $CsvExportPath" -ForegroundColor Gray

        # Export control summary
        Export-ControlSummaryToCsv -ControlSummaryData $ControlSummaryData -OutputDirectory $CsvExportPath

        # Export permission data
        Export-PermissionsToCsv -AuditData $AuditDataHash -OutputDirectory $CsvExportPath

        # Export administrator data
        Export-AdministratorsToCsv -AuditData $AuditDataHash -OutputDirectory $CsvExportPath

        Write-Host "  CSV exports completed successfully" -ForegroundColor Green
    }
    catch {
        Write-Host "[ERROR] Failed to export CSV files: $($_.Exception.Message)" -ForegroundColor Red
    }
} else {
    Write-Host "Step 6/6: Skipping CSV export (not requested)" -ForegroundColor Yellow
}

# ================================================================================
# COMPLETION AND BROWSER LAUNCH
# ================================================================================

$ReportEndTime = Get-Date
$ReportDuration = $ReportEndTime - $ReportStartTime

Write-Host ""
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Exchange Mailbox Security Audit Report Generation Completed" -ForegroundColor Green
Write-Host "=================================================================================" -ForegroundColor Green
Write-Host "Report ID: $ReportID" -ForegroundColor Cyan
Write-Host "Source Audit ID: $($AuditMetadata.AuditID)" -ForegroundColor Cyan
Write-Host "Generation Time: $($ReportEndTime.ToString('yyyy-MM-dd HH:mm:ss'))" -ForegroundColor Cyan
Write-Host "Duration: $([math]::Round($ReportDuration.TotalSeconds, 2)) seconds" -ForegroundColor Cyan
Write-Host "HTML Report: $OutputPath" -ForegroundColor Cyan
if ($ExportCsv) {
    Write-Host "CSV Exports: $CsvExportPath" -ForegroundColor Cyan
}
Write-Host ""
Write-Host "[SUCCESS] Report generation completed successfully" -ForegroundColor Green
Write-Host "[SUCCESS] Open the HTML file in your web browser to view the report" -ForegroundColor Green

# Display summary statistics
Write-Host ""
Write-Host "REPORT SUMMARY:" -ForegroundColor Yellow
Write-Host "- Organization: $($AuditMetadata.OrganizationName)" -ForegroundColor White
Write-Host "- Overall Compliance Score: $OverallComplianceScore%" -ForegroundColor $(if ($OverallComplianceScore -ge 80) { "Green" } elseif ($OverallComplianceScore -ge 60) { "Yellow" } else { "Red" })
Write-Host "- Total Controls Assessed: $TotalControls" -ForegroundColor White
Write-Host "- Compliant Controls: $CompliantControls" -ForegroundColor Green
Write-Host "- Controls Requiring Review: $ReviewRequiredControls" -ForegroundColor Yellow
Write-Host "- Non-Compliant Controls: $NonCompliantControls" -ForegroundColor Red
if ($ErrorControls -gt 0) {
    Write-Host "- Controls with Errors: $ErrorControls" -ForegroundColor Magenta
}
Write-Host "- Risk Distribution: Critical: $CriticalRiskControls | High: $HighRiskControls | Medium: $MediumRiskControls | Low: $LowRiskControls" -ForegroundColor White

# Priority recommendations summary
$HighPriorityCount = ($ControlSummaryData | Where-Object { $_.RiskLevel -in @("Critical", "High") -and $_.ComplianceStatus -ne "Compliant" }).Count
$MediumPriorityCount = ($ControlSummaryData | Where-Object { $_.RiskLevel -eq "Medium" -and $_.ComplianceStatus -ne "Compliant" }).Count

if ($HighPriorityCount -gt 0) {
    Write-Host ""
    Write-Host "⚠️  IMMEDIATE ATTENTION REQUIRED: $HighPriorityCount high-priority security issues identified" -ForegroundColor Red
} elseif ($MediumPriorityCount -gt 0) {
    Write-Host ""
    Write-Host "📋 REVIEW RECOMMENDED: $MediumPriorityCount medium-priority items require attention" -ForegroundColor Yellow
} else {
    Write-Host ""
    Write-Host "✅ EXCELLENT SECURITY POSTURE: All controls are within acceptable compliance thresholds" -ForegroundColor Green
}

Write-Host "=================================================================================" -ForegroundColor Green

# Launch browser if not skipped
if (-not $SkipBrowser) {
    try {
        Write-Host ""
        Write-Host "Opening report in default web browser..." -ForegroundColor Yellow
        Start-Process $OutputPath
    }
    catch {
        Write-Host "[WARNING] Could not open browser automatically: $($_.Exception.Message)" -ForegroundColor Yellow
        Write-Host "Please manually open: $OutputPath" -ForegroundColor Yellow
    }
}

<#
================================================================================
EXECUTION INSTRUCTIONS FOR ADMIN:
================================================================================

1. BASIC USAGE (Process latest JSON file):
   .\Exchange-Audit-Report-Generator.ps1

2. PROCESS SPECIFIC JSON FILE:
   .\Exchange-Audit-Report-Generator.ps1 -JsonFilePath "C:\Path\To\Results.json"

3. CUSTOM OUTPUT LOCATION:
   .\Exchange-Audit-Report-Generator.ps1 -OutputPath "C:\Reports\Audit-Report.html"

4. FILTER BY RISK LEVEL:
   .\Exchange-Audit-Report-Generator.ps1 -RiskFilter "High,Critical"

5. FILTER BY CONTROL TYPE:
   .\Exchange-Audit-Report-Generator.ps1 -ControlFilter "MBX-1.1,MBX-2.1"

6. EXPORT CSV FILES:
   .\Exchange-Audit-Report-Generator.ps1 -ExportCsv

7. CONSOLE OUTPUT ONLY:
   .\Exchange-Audit-Report-Generator.ps1 -ConsoleOutput -SkipBrowser

8. COMBINED PARAMETERS:
   .\Exchange-Audit-Report-Generator.ps1 -JsonFilePath "Results.json" -ExportCsv -RiskFilter "High,Critical" -OutputPath "Critical-Issues-Report.html"

FEATURES:
- Professional HTML report with responsive design
- Executive dashboard with compliance metrics
- Detailed security control analysis
- Permission analysis tables with risk assessment
- Cross-domain relationship mapping
- Administrator discovery reporting
- Prioritized actionable recommendations
- CSV export capabilities for detailed analysis
- Print-friendly formatting
- Mobile-responsive design

PREREQUISITES:
- PowerShell 5.1 or later
- JSON output file from Exchange-Mailbox-Security-Audit.ps1 v1.6.6
- Web browser for viewing HTML reports
- Excel or similar for CSV analysis (optional)

IMPORTANT NOTES:
- This script is 100% READ-ONLY and processes existing audit data
- No Exchange configuration modifications are performed
- Reports contain sensitive security information - handle appropriately
- Compatible with Exchange-Mailbox-Security-Audit.ps1 version 1.6.6
- All operations are safe for production environments

REPORT SECTIONS:
1. Executive Dashboard - Overall compliance status and key metrics
2. Security Control Analysis - Detailed assessment of each control
3. Permission Analysis Tables - Full Access, Send-As, Send-On-Behalf
4. Cross-Domain Risk Assessment - Cross-domain relationships and risks
5. Administrator Discovery Report - Role assignments and domain mapping
6. Actionable Recommendations - Prioritized remediation actions

OUTPUT FORMATS:
- Primary: Professional HTML report with CSS styling
- Secondary: Console summary with color-coded status
- Optional: CSV exports for detailed data analysis in Excel

================================================================================
#>

# ================================================================================
# **Created By:** E.Z. Consultancy
# **Script Version:** 1.0.0
# **Exchange Version:** Exchange Server 2016/2019/Online Compatible
# 🏛️ **Authority:** Internal Audit
# ================================================================================
